import { getMac } from "@/utils/pandaThird";
import { PageContainer } from "@ant-design/pro-components";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Checkbox, Col, ConfigProvider, Dropdown, Form, Input, MenuProps, message, Modal, PaginationProps, Row, Space, Table } from "antd";
import { ColumnsType, ColumnType } from "antd/es/table";
import React, { useEffect, useImperativeHandle, useState, useCallback, useMemo, useLayoutEffect, useRef } from "react";
import { request } from "@/utils/request";
import confirmRequest from "@/utils/confirmRequest";
import { DeleteOutlined, DownOutlined } from "@ant-design/icons";
import ExcelDesign from "@/components/System/ExcelDesign";
import { buildDynamicFormItem, FormItem } from "@/components/CustomComponent/DynamicFormBuilder";
import moment from "moment";
import { resolvePath } from "@/utils/urlUtils";
import PageDesign from "@/components/System/PageDesign";

// Types
interface ApiResponse<T = any> {
    success: boolean;
    data: T;
    message?: string;
}

interface TableData {
    data: any[];
    total: number;
    size: number;
    current: number;
}


interface TableState {
    total: number;
    current: number;
    pageSize: number;
    dataList: any[];
    loading: boolean;
    formData: Record<string, any>;
    sortField?: string;
    sortOrder?: "ascend" | "descend" | null;
}

interface TableColumnProps {
    currency?: boolean;// 货币类型
    time?: boolean;// 时间类型  
    deleteColumn?: boolean;// 删除类型
    deletePath?: string;// 删除路径
    deleteMessage?: string;// 删除消息
    IsSort?: boolean;// 排序类型
    show?: boolean;// 是否显示
    IsDate?: boolean;// 日期类型
    IsTime?: boolean;// 时间类型
}
interface BatchOperation {
    icon?: React.ReactNode;
    key: string;
    label: React.ReactNode;
    onClick?: (selectedKeys: React.Key[], selectedRows: any[]) => void;
    fields?: FormItem[]; // 使用你定义的 FormItem 类型
    postUrl?: string; // POST 请求地址
    putUrl?: string; // PUT 请求地址
    idField?: string; // 自定义 ID 字段名，默认为 'Ids'
}

export type MyTableColumnProps = ColumnType<any> & TableColumnProps;

interface TableProps {
    formCols?: FormItem[];
    formButtons?: React.ReactNode[]; // 搜索框里面的 Buttons
    formInitData?: any; //查询的Form 默认的数据
    needSearchKey?: boolean;
    columns?: MyTableColumnProps[];
    // buttons?: React.ReactNode[];
    search?: (params: any) => Promise<TableData>;
    getPath?: string;
    tableParams?: any; //table 查询的时候 需要带上的 参数
    rowKey: string;
    tableButtons?: React.ReactNode[];
    firstTableButtons?: React.ReactNode[]; // 新增属性，优先级最高的按钮
    downloadPath?: string;
    downloadTableName?: string;
    downloadSelectRowKeysName?: string; // 新增属性，用于导出选中行的参数名
    noPage?: boolean; // 不分页
    transformedData?: (data: any[]) => void;
    rowSelection?: {
        selectItems?: React.Key[];
        selectedRowKeys?: React.Key[];
        setSelectItems?: (items: React.Key[]) => void;
        setSelectedRowKeys?: (keys: React.Key[]) => void;
        onChange?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void;
        getCheckboxProps?: (record: any) => { disabled?: boolean; name?: string };
    };
    // 新增批量操作菜单项
    batchOperations?: BatchOperation[];
    batchMenu?: MenuProps | undefined;
    onBatchOperations?: () => void;
    balance?: any; // Add this new prop
    showBalance?: (response: any) => Promise<string>; // 添加新的方法属性
    setSearchParams?: (parmas: any[]) => void;
    onLoadCallback?: (params: any, response: any) => void;
    dataSource?: any[]; // Add this new prop
    autoLoad?: boolean; // 是否在页面打开时自动加载数据
    tableDesignName?: string; // 添加表设计名称属性
    tableDesignRender?: (text: any, record: any, config: any) => React.ReactNode; // 添加自定义渲染方法
    getCustomColumns?: (columns: MyTableColumnProps[]) => Promise<MyTableColumnProps[]>; // 修改为异步方法并重命名
}

export interface TableMethods {
    reload: () => void;
    setSelectItems: (selectedKeys: React.Key[]) => void;
}

// Constants
const DEFAULT_PAGE_SIZE = 15;
const PAGE_SIZE_OPTIONS = [15, 30, 100, 200, 500];

const isTableData = (data: any): data is TableData => {
    return data && Array.isArray(data.data) && typeof data.total === "number" && typeof data.size === "number" && typeof data.current === "number";
};

export interface FormColType {
    label: string;
    name: string;
    type: string;
    search?: boolean;
    placeholder?: string;
    request?: () => Promise<any>;
}

const MyTable = React.forwardRef<TableMethods, TableProps>((props, ref) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [searchForm] = Form.useForm();

    const [initialized, setInitialized] = useState(false);
    const [tableColumns, setTableColumns] = useState<MyTableColumnProps[]>(props.columns || []);

    const [tableState, setTableState] = useState<TableState>({
        total: 0,
        current: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        dataList: [],
        loading: false,
        formData: {},
    });

    const [formCols, setFormCols] = useState<React.ReactNode[]>([]);

    // const showTotal: Required<PaginationProps>["showTotal"] = useCallback((total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`, []);

    const [keyIndex, setKeyIndex] = useState(1);

    const updateTableState = useCallback((newState: Partial<TableState>) => {
        setTableState((prev) => ({ ...prev, ...newState }));
    }, []);

    const load = useCallback(
        async (form: Record<string, any>, page: number, size: number, sortInfo?: { sortField?: string; sortOrder?: string }) => {
            // If dataSource is provided, use it directly
            if (props.dataSource) {
                updateTableState({
                    dataList: props.dataSource,
                    loading: false,
                    current: undefined,
                    total: props.dataSource.length
                });
                return;
            }


            if (props.noPage == true) {
                size = 99999;
            }
            updateTableState({
                current: page,
                formData: form,
                pageSize: size,
                loading: true,
                dataList: [],
            });
            setBalance("");

            try {
                let tableData: TableData | null = null;

                console.log('form');
                console.log(form);

                const requestParams = {
                    ...form,
                    ...props.tableParams,
                    pageSize: size,
                    current: page,
                    sortField: sortInfo?.sortField ?? tableState.sortField,
                    sortOrder: sortInfo?.sortOrder ?? tableState.sortOrder,
                };

                if (props.search) {
                    const searchResult = await props.search(requestParams);
                    if (isTableData(searchResult)) {
                        tableData = searchResult;
                    }
                } else if (props.getPath) {
                    const response = await request<ApiResponse<TableData | { data: TableData }>>(props.getPath, {
                        method: "POST",
                        data: requestParams,
                    });
                    if (response.success) {
                        if (response.data) {
                            if (Array.isArray(response.data)) {
                                tableData = { data: response.data, current: -1, size: -1, total: -1 };
                            } else if (isTableData(response.data)) {
                                tableData = response.data;
                            } else if (Array.isArray(response.data.data)) {
                                tableData = { data: response.data.data, current: -1, size: -1, total: -1 };
                            } else if (isTableData(response.data.data)) {
                                tableData = response.data.data;
                            }
                        }
                        // 调用回调函数
                        if (props.onLoadCallback) {
                            props.onLoadCallback({ ...form, ...props.tableParams, pageSize: size, current: page }, response);
                        }
                        // 如果提供了 showBalance 方法，调用它来更新余额
                        if (props.showBalance) {
                            try {
                                const balanceValue = await props.showBalance(response);
                                setBalance(balanceValue); // 需要添加一个新的 state 来存储余额
                            } catch (error) {
                                // console.error("Failed to fetch balance:", error);
                            }
                        }
                    } else {
                        return;
                    }
                }

                if (tableData) {
                    // 添加数据转换逻辑
                    const transformedData: any = props.transformedData ? props.transformedData(tableData.data) : tableData.data;

                    if (tableData.current > 0) {
                        updateTableState({
                            dataList: transformedData,
                            current: tableData.current,
                            pageSize: tableData.size,
                            total: tableData.total,
                        });
                    } else {
                        updateTableState({
                            dataList: transformedData,
                            current: undefined,
                        });
                    }
                } else {
                    // messageApi.error("数据格式错误");
                }
            } catch (error) {
                // console.error("Load data error:", error);
                messageApi.error("加载数据失败");
            } finally {
                updateTableState({ loading: false });
            }
        },
        [props.search, props.getPath, props.transformedData, messageApi, props.dataSource],
    );

    // 扩展 reload 方法，可以选择是否保留选中状态
    const reload = useCallback(
        (keepSelection: boolean = true) => {
            if (!keepSelection) {
                setSelectedRowKeys([]);
            }
            load(tableState.formData, tableState.current, tableState.pageSize);
        },
        [load, tableState.formData, tableState.current, tableState.pageSize],
    );

    // 暴露选中项和反选的方法
    useImperativeHandle(ref, () => ({
        reload,
        setFormData: (e: string, f: any) => {
            searchForm.setFieldValue(e, f);
        },
        getSelectedRowKeys: () => selectedRowKeys,
        clearSelection: () => setSelectedRowKeys([]),
        invertSelection: handleInvertSelection,
        setSelectItems: (selectedKeys: React.Key[]) => setSelectedRowKeys(selectedKeys),
    }));
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRow, setSelectedRow] = useState<any>(null);

    const handleExport = useCallback(
        async (designId: string, tableId: string) => {
            if (!props.downloadPath) {
                messageApi.error("请先配置相应的下载对象");
                return;
            }

            // 构建导出参数
            const exportParams: Record<string, any> = {
                ...tableState.formData,
                designId,
                tableId,
            };

            // 获取选中的行keys，优先使用外部传入的selectedRowKeys
            const currentSelectedKeys = props.rowSelection?.selectedRowKeys || selectedRowKeys;

            // 如果有选中行且指定了参数名，则添加选中行的参数
            if (props.downloadSelectRowKeysName && currentSelectedKeys.length > 0) {
                exportParams[props.downloadSelectRowKeysName] = currentSelectedKeys;
            }

            console.log('exportParams:', JSON.stringify(exportParams));

            confirmRequest.post({
                message: "确定要导出当前数据吗?",
                setPath: props.downloadPath,
                data: exportParams,
                timeout: 999999999,
                method: "POST",
                responseType: "blob",
                onCallBack: () => { },
                messageApi,
            });
        },
        [props.downloadPath, props.downloadSelectRowKeysName, tableState.formData, messageApi, props.rowSelection?.selectedRowKeys, selectedRowKeys],
    );

    // 添加加载列配置的函数
    const loadColumnConfig = useCallback(async () => {
        if (!props.tableDesignName) {
            // 如果有自定义列方法，先执行它
            if (props.getCustomColumns) {
                const customColumns = await props.getCustomColumns(props.columns || []);
                setTableColumns(customColumns);
            } else {
                setTableColumns(props.columns || []);
            }
            return;
        }

        try {
            // 如果有自定义列方法，先执行它
            let baseColumns = props.columns || [];
            if (props.getCustomColumns) {
                baseColumns = await props.getCustomColumns(baseColumns);
            }

            console.log('baseColumns');
            console.log(baseColumns);

            const columnsResponse = await request("/SystemManage/PageDesign/getPageUserDesign", {
                method: "POST",
                data: {
                    PageName: props.tableDesignName,
                },
                errorMessage: false,
            });

            if (columnsResponse.success && columnsResponse.data) {
                // Find the CustomColumns configuration
                const customColumnsConfig = columnsResponse.data.find((config: any) => config.FieldName === 'CustomColumns');
                const customColumnsIndex = customColumnsConfig?.ColumnIndex || 9999;

                // Create base columns from API response
                const newColumns = columnsResponse.data
                    .filter((config: any) => config.FieldName !== 'CustomColumns')
                    .map((config: any) => ({
                        title: config.ColumnName,
                        dataIndex: config.FieldName,
                        key: config.FieldName,
                        width: config.ColumnWidth,
                        show: config.ColumnShow,
                        IsSort: config.IsSort,
                        IsDate: config.IsDate,
                        IsTime: config.IsTime,
                        fixed: config.Fixed === 'right' || config.Fixed === 'left' ? config.Fixed : undefined,
                        align: 'center',
                        columnIndex: config.ColumnIndex || 9999,
                        render: (text: any, record: any) => {
                            if (config.IsDate || config.IsTime) {
                                if (!text || moment(text) <= moment('1901-01-01')) {
                                    return '';
                                }
                                if (config.IsDate) {
                                    return moment(text).format('YYYY-MM-DD');
                                }
                                if (config.IsTime) {
                                    return moment(text).format('YYYY-MM-DD HH:mm:ss');
                                }
                            }
                            if (props.tableDesignRender) {
                                return props.tableDesignRender(text, record, config);
                            }
                            return text;
                        }
                    }));

                // 添加自定义列到 newColumns
                if (baseColumns && baseColumns.length > 0) {
                    const customColumnsWithIndex = baseColumns.map(col => ({
                        ...col,
                        columnIndex: customColumnsIndex
                    }));
                    newColumns.push(...customColumnsWithIndex);
                }

                // Sort columns based on columnIndex and fixed property
                const sortedColumns = newColumns
                    .sort((a: any, b: any) => {
                        // First sort by Fixed property
                        const getFixedPriority = (fixed: string | undefined) => {
                            if (fixed === 'left') return 0;
                            if (fixed === undefined) return 1;
                            if (fixed === 'right') return 2;
                            return 1;
                        };

                        const fixedPriorityA = getFixedPriority(a.fixed);
                        const fixedPriorityB = getFixedPriority(b.fixed);

                        if (fixedPriorityA !== fixedPriorityB) {
                            return fixedPriorityA - fixedPriorityB;
                        }

                        // Then sort by columnIndex
                        return (b.columnIndex || 9999) - (a.columnIndex || 9999);
                    })
                    .filter((column: any) => column.show !== false);

                setTableColumns(sortedColumns);
            } else {
                setTableColumns(baseColumns);
            }
        } catch (error) {
            console.error("Failed to load column configuration:", error);
            setTableColumns(props.columns || []);
        }
    }, [props.tableDesignName, props.columns, props.tableDesignRender, props.getCustomColumns]);

    // 在组件挂载时加载列配置
    useEffect(() => {
        loadColumnConfig();
    }, []);

    // 修改 processedColumns 使用 tableColumns 而不是 props.columns
    const processedColumns = useMemo(() => {
        return tableColumns.map((column) => {
            // 先创建一个基础列配置
            const baseColumn = {
                align: "center", // 默认居中
                ...column, // 允许原有配置覆盖默认值
                sorter: column.IsSort ? true : undefined,
            };

            // 确保 fixed 属性被正确设置
            if (column.fixed === 'right' || column.fixed === 'left') {
                baseColumn.fixed = column.fixed;
            }

            const finalColumn = {
                ...baseColumn,
                render: column.deleteColumn
                    ? (dom: any, record: any) => (
                        <a
                            key="delete"
                            onClick={() => {
                                const params = { [column.dataIndex || ""]: dom };
                                // 处理 URL 参数替换
                                const resolvedPath = column.deletePath ? resolvePath(column.deletePath, record) : "";
                                confirmRequest.post({
                                    message: column.deleteMessage || "确定要删除吗？",
                                    setPath: resolvedPath,
                                    data: params,
                                    method: "DELETE",
                                    onCallBack: reload,
                                    messageApi,
                                });
                            }}
                            title="删除"
                        >
                            <DeleteOutlined />
                        </a>
                    )
                    : column.currency
                        ? (dom: any) => dom?.toFixed(2)
                        : column.time
                            ? (dom: any) => (moment(dom) < moment("2000-01-01") ? "" : dom)
                            : column.IsDate || column.IsTime
                                ? (dom: any) => {
                                    if (!dom || moment(dom) <= moment('1901-01-01')) {
                                        return '';
                                    }
                                    if (column.IsDate) {
                                        return moment(dom).format('YYYY-MM-DD');
                                    }
                                    if (column.IsTime) {
                                        return moment(dom).format('YYYY-MM-DD HH:mm:ss');
                                    }
                                    return dom;
                                }
                                : column.render,
            };

            return finalColumn;
        });
    }, [tableColumns, reload, messageApi]);

    useLayoutEffect(() => {
        const initFormCols = () => {
            const cols: React.ReactNode[] = [];

            if (props.formCols !== undefined && (props.formCols.length === 0 || props.needSearchKey)) {
                cols.push(
                    <Col span={8} key="search-key-col">
                        <Form.Item name="SearchKey" label="关键字词" initialValue="" style={{ width: "100%", marginBottom: "12px" }}>
                            <Input placeholder="关键字搜索" allowClear />
                        </Form.Item>
                    </Col>,
                );
            }

            let values = {};
            if (props.formInitData != undefined) {
                values = props.formInitData;
            }

            if (props.formCols && props.formCols != undefined) {
                cols.push(
                    ...props.formCols.map((item, index) => {
                        item.width = "100%";

                        return (
                            <Col span={8} key={`form-col-${index}`} style={{ width: "100%", marginBottom: "12px" }}>
                                {buildDynamicFormItem({
                                    item,
                                    values,
                                    formInstance: searchForm,
                                    keyIndex,
                                    messageApi,
                                })}
                            </Col>
                        );
                    }),
                );
            }

            if (cols.length > 0) {
                const remainder = cols.length % 3;
                if (remainder === 0) {
                    cols.push(<Col span={8} key="empty-1" />);
                    cols.push(<Col span={8} key="empty-2" />);
                }
                if (remainder === 1) {
                    // cols.push(<Col span={8} key="empty-1" />);
                    cols.push(<Col span={8} key="empty-2" />);
                } else if (remainder === 2) {
                    // cols.push(<Col span={8} key="empty-1" />);
                }
            }

            // console.log("cols");
            // console.log(cols);

            setFormCols(cols);
        };

        initFormCols();
    }, [props.formCols, props.needSearchKey]);

    // 初始化数据加载
    useEffect(() => {
        if (initialized) return; // 如果已经初始化过，直接返回

        const shouldUseForm = props.formCols !== undefined && props.formCols.length > 0;

        // 等待一个渲染周期，确保表单已经准备好
        const timer = setTimeout(() => {
            // 只有当 autoLoad 为 true 时才自动加载数据
            if (props.autoLoad || props.autoLoad == undefined) {
                if (shouldUseForm) {
                    searchForm.submit();
                } else {
                    load({}, 1, DEFAULT_PAGE_SIZE);
                }
            }
            setInitialized(true);
        }, 500);

        return () => clearTimeout(timer);
    }, [formCols]); // 只依赖 formCols

    // 处理选择变化
    const handleSelectionChange = (newSelectedRowKeys: React.Key[], selectedRows: any[]) => {
        // 使用传入的 set 方法更新状态
        setSelectedRowKeys(newSelectedRowKeys);
        props.rowSelection?.setSelectedRowKeys?.(newSelectedRowKeys);
        props.rowSelection?.setSelectItems?.(newSelectedRowKeys);

        // 调用额外的 onChange 回调（如果存在）
        props.rowSelection?.onChange?.(newSelectedRowKeys, selectedRows);
    };
    // 处理反选逻辑
    const handleInvertSelection = () => {
        const availableKeys = tableState.dataList
            .filter((record) => {
                const checkboxProps = props.rowSelection?.getCheckboxProps?.(record) || {};
                return !checkboxProps.disabled;
            })
            .map((record) => record[props.rowKey]);

        const newSelectedKeys = availableKeys.filter((key) => !selectedRowKeys.includes(key));

        setSelectedRowKeys(newSelectedKeys);
        const selectedRows = tableState.dataList.filter((record) => newSelectedKeys.includes(record[props.rowKey]));
        props.rowSelection?.onChange?.(newSelectedKeys, selectedRows);
    };

    // 构建完整的 rowSelection 配置
    const rowSelection = props.rowSelection
        ? {
            selectedRowKeys: props.rowSelection.selectedRowKeys,
            onChange: handleSelectionChange,
            preserveSelectedRowKeys: true,
            selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
            getCheckboxProps: props.rowSelection.getCheckboxProps,
        }
        : undefined;

    // 同步外部选中状态
    useEffect(() => {
        if (props.rowSelection?.selectedRowKeys) {
            setSelectedRowKeys(props.rowSelection.selectedRowKeys);
        }
    }, [props.rowSelection?.selectedRowKeys]);

    const [dontClearSelected, setDontClearSelected] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [currentOperation, setCurrentOperation] = useState<BatchOperation | null>(null);
    const [modalForm] = Form.useForm();
    const [modalLoading, setModalLoading] = useState(false);

    // 处理批量操作点击
    const handleBatchOperation = (operation: BatchOperation) => {
        if (operation.onClick) {
            const selectedRows = tableState.dataList.filter((record) => selectedRowKeys.includes(record[props.rowKey]));
            operation.onClick(selectedRowKeys, selectedRows);
        } else if (operation.fields && (operation.postUrl || operation.putUrl)) {
            setCurrentOperation(operation);
            setModalVisible(true);
        }
    };

    // 处理表单提交
    const handleModalSubmit = async () => {
        if (!currentOperation) return;

        try {
            setModalLoading(true);
            const values = await modalForm.validateFields();

            // 添加选中的 IDs
            const idField = currentOperation.idField || "Ids";
            values[idField] = selectedRowKeys;

            const response = await request((currentOperation.postUrl || currentOperation.putUrl)!, {
                method: currentOperation.postUrl ? "POST" : "PUT",
                data: values,
            });

            if (response.success) {
                messageApi.success("操作成功");
                setModalVisible(false);
                modalForm.resetFields();
                if (!dontClearSelected) {
                    setSelectedRowKeys([]);
                    props.rowSelection?.onChange?.([], []);
                }
                reload();
            } else {
                messageApi.error(response.message || "操作失败");
            }
        } catch (error) {
            // console.error("Form submission error:", error);
            // messageApi.error("表单提交失败");
        } finally {
            setModalLoading(false);
        }
    };
    // 渲染模态框
    const renderModal = () => (
        <Modal
            title={currentOperation?.label}
            open={modalVisible}
            onCancel={() => {
                setModalVisible(false);
                modalForm.resetFields();
            }}
            onOk={handleModalSubmit}
            confirmLoading={modalLoading}
        >
            <Form
                form={modalForm}
                layout="vertical"
                style={{
                    marginTop: "20px",
                }}
            >
                {currentOperation?.fields?.map((field, index) =>
                    buildDynamicFormItem({
                        item: field,
                        values: {},
                        formInstance: modalForm,
                        keyIndex: index,
                        messageApi: message,
                    }),
                )}
            </Form>
        </Modal>
    );

    // 渲染批量操作区域
    const renderBatchOperations = () => {
        if (!selectedRowKeys.length) return null;

        return (
            <Alert
                message={
                    <Space size={24}>
                        {props.batchMenu && props.batchMenu.items && props.batchMenu.items.length > 0 && (
                            <Dropdown menu={props.batchMenu}>
                                <a>
                                    批量操作 <DownOutlined />
                                </a>
                            </Dropdown>
                        )}
                        {!props.batchMenu && props.onBatchOperations && (
                            <a onClick={() => props.onBatchOperations?.()}>
                                批量操作
                            </a>
                        )}
                        {!props.batchMenu && props.batchOperations && (() => {
                            const operations = props.batchOperations;
                            return operations.length <= 3 ? (
                                <Space>
                                    {operations.map((operation) => (
                                        <a
                                            key={operation.key}
                                            onClick={() => handleBatchOperation(operation)}
                                        >
                                            {operation.icon && <span style={{ marginRight: 8 }}>{operation.icon}</span>}
                                            {operation.label}
                                        </a>
                                    ))}
                                </Space>
                            ) : (
                                <Dropdown
                                    menu={{
                                        items: operations.map((operation) => ({
                                            key: operation.key,
                                            label: operation.label,
                                        })),
                                        onClick: ({ key }) => {
                                            const operation = operations.find((op) => op.key === key);
                                            if (operation) {
                                                handleBatchOperation(operation);
                                            }
                                        },
                                    }}
                                >
                                    <a>批量操作</a>
                                </Dropdown>
                            );
                        })()}
                        <span>
                            已选 {selectedRowKeys.length} 项
                            <a
                                style={{ marginInlineStart: 8 }}
                                onClick={() => {
                                    // 更新所有相关的选择状态
                                    setSelectedRowKeys([]);
                                    props.rowSelection?.setSelectedRowKeys?.([]);
                                    props.rowSelection?.setSelectItems?.([]);
                                    props.rowSelection?.onChange?.([], []);
                                }}
                            >
                                取消选择
                            </a>
                        </span>
                        <span>
                            <Checkbox
                                checked={dontClearSelected}
                                onChange={(e) => {
                                    setDontClearSelected(e.target.checked);
                                }}
                            >
                                不要清空我的选择
                            </Checkbox>
                        </span>
                    </Space>
                }
                style={{
                    marginBottom: 10,
                    background: "rgba(0, 0, 0, 0.02)",
                    border: "1px solid rgba(0, 0, 0, 0.05)",
                }}
            />
        );
    };

    // 添加一个 ref 来存储最新的排序状态
    const sortRef = useRef<{ sortField?: string; sortOrder?: string }>({});

    // 修改 handleTableChange
    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        // 如果是分页变化，不处理，让 pagination 的 onChange 处理
        if (sorter && (sorter.field || sorter.order)) {
            const { field, order } = sorter;

            // 更新 ref 中的排序状态
            sortRef.current = {
                sortField: field,
                sortOrder: order,
            };

            // 使用当前页码进行加载
            load(tableState.formData, pagination.current, pagination.pageSize, sortRef.current);

            // 更新状态
            updateTableState({
                sortField: field,
                sortOrder: order,
            });
        }
    };

    // Add transform function for MyColumnData
    const transformFormData = (values: any) => {
        const transformedValues = { ...values };
        const myColumnData: any = {};

        // Find all keys that match MyColumnData[UUID] pattern
        Object.keys(values).forEach(key => {
            const match = key.match(/^MyColumnData\[([a-f0-9-]+)\]$/i);
            if (match) {
                const uuid = match[1];
                const value = values[key];
                myColumnData[uuid] = value;
                delete transformedValues[key];
            }
        });

        // If we found any MyColumnData entries, add them to the transformed values
        if (Object.keys(myColumnData).length > 0) {
            transformedValues.MyColumnData = myColumnData;
        }

        return transformedValues;
    };

    const onFinish = useCallback(
        async (values: any) => {
            // Only clear selection if dontClearSelected is false
            if (!dontClearSelected) {

                setSelectedRowKeys([]);
                props.rowSelection?.setSelectedRowKeys?.([]);
                props.rowSelection?.setSelectItems?.([]);
                props.rowSelection?.onChange?.([], []);
            }

            // Get all form values including those not in the form data
            const allFields = searchForm.getFieldsValue(true);

            // Transform the form data
            const transformedValues = transformFormData(allFields);

            // Merge with any additional parameters
            const params = {
                ...transformedValues,
                MacAddress: "test-mac",
                ...sortRef.current,
            };

            if (props.setSearchParams) {
                props.setSearchParams(params);
            }
            // 传入排序参数
            load(params, 1, tableState.pageSize, sortRef.current);
        },
        [searchForm, dontClearSelected, props.rowSelection],
    );

    const renderSearchForm = () => (
        <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 0 } } }}>
            <Card className="no-padding">
                <Form form={searchForm} name="advanced-search" style={{ maxWidth: "none", padding: "20px 12px 8px 20px" }} onFinish={onFinish}>
                    <Row gutter={24}>
                        {formCols.map((col, index) => (
                            <React.Fragment key={`form-col-${index}`}>{col}</React.Fragment>
                        ))}
                        <Col span={8} style={{ textAlign: "right", float: "right", marginBottom: "12px" }}>
                            <Space>
                                {props.formButtons}
                                <Button
                                    onClick={() => {
                                        searchForm.resetFields();
                                        searchForm.submit();
                                    }}
                                >
                                    重置
                                </Button>
                                <Button type="primary" htmlType="submit" loading={tableState.loading}>
                                    查询
                                </Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
            </Card>
        </ConfigProvider>
    );
    // 添加新的 state 来存储余额
    const [balance, setBalance] = useState<string>("");
    const [isTextOverflow, setIsTextOverflow] = useState(false);
    const textRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const checkOverflow = () => {
            if (textRef.current) {
                setIsTextOverflow(textRef.current.scrollWidth > textRef.current.clientWidth);
            }
        };

        checkOverflow();
        window.addEventListener("resize", checkOverflow);
        return () => window.removeEventListener("resize", checkOverflow);
    }, [balance, props.balance]);

    const renderTable = () => (
        <>
            {renderBatchOperations()}
            <div style={{ position: "relative" }}>
                <Table<any>
                    className="custom-table"
                    scroll={{ x: "max-content" }}
                    rowKey={props.rowKey}
                    rowSelection={rowSelection}
                    size="small"
                    columns={processedColumns as ColumnsType<any>}
                    loading={tableState.loading}
                    dataSource={tableState.dataList}
                    onRow={(record) => ({
                        onClick: () => {
                            setSelectedRow(record);
                        },
                        className: selectedRow && selectedRow[props.rowKey] === record[props.rowKey] ? 'ant-table-row-selected' : '',
                        style: {
                            cursor: 'pointer'
                        }
                    })}
                    pagination={
                        ((props.noPage == undefined || props.noPage == false) && !props.dataSource) &&
                        tableState.current != undefined && {
                            total: tableState.total,
                            onChange: (page, pageSize) => {
                                // Clear selection on page change only if dontClearSelected is false
                                if (!dontClearSelected) {
                                    setSelectedRowKeys([]);
                                    props.rowSelection?.setSelectedRowKeys?.([]);
                                    props.rowSelection?.setSelectItems?.([]);
                                    props.rowSelection?.onChange?.([], []);
                                }

                                // Only call load when page or pageSize changes
                                if (page !== tableState.current || pageSize !== tableState.pageSize) {
                                    load(tableState.formData, page, pageSize, sortRef.current);
                                }
                            },
                            showTotal: (total) => (
                                <div
                                    style={{
                                        background: "#fff",
                                        padding: "0 8px",
                                        position: "relative",
                                        zIndex: 2,
                                    }}
                                >
                                    共 {total} 条记录
                                </div>
                            ),
                            pageSize: tableState.pageSize,
                            current: tableState.current,
                            showSizeChanger: true,
                            pageSizeOptions: PAGE_SIZE_OPTIONS,
                            style: {
                                position: "relative",
                                zIndex: 2,
                            },
                        }
                    }
                    onChange={handleTableChange}
                />
                {/* 在表格下方单独显示余额 */}
                <div
                    style={{
                        display: "flex",
                        justifyContent: "flex-start",
                        padding: "16px 0",
                        position: "absolute",
                        bottom: 0,
                        left: 0,
                        maxWidth: "calc(100% - 450px)", // 只预留分页器的空间
                        overflow: "hidden",
                        zIndex: 1,
                    }}
                >
                    <div
                        style={{
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            width: "100%",
                        }}
                    >
                        {balance || props.balance}
                    </div>
                </div>
            </div>
        </>
    );

    // Add this style block at the beginning of the component
    const tableStyle = `
        .custom-table .ant-table-tbody > tr:hover > td {
            background-color: #d9d9d9 !important;
        }
        .custom-table .ant-table-tbody > tr.ant-table-row-selected > td {
            background-color: #bae7ff !important;
        }
        .custom-table .ant-table-tbody > tr.ant-table-row-selected:hover > td {
            background-color: #bae7ff !important;
        }
        .custom-table .ant-table-fixed-left .ant-table-tbody > tr.ant-table-row-selected > td,
        .custom-table .ant-table-fixed-right .ant-table-tbody > tr.ant-table-row-selected > td {
            background-color: #bae7ff !important;
        }
        .custom-table .ant-table-fixed-left .ant-table-tbody > tr.ant-table-row-selected:hover > td,
        .custom-table .ant-table-fixed-right .ant-table-tbody > tr.ant-table-row-selected:hover > td {
            background-color: #bae7ff !important;
        }
    `;

    return (
        <>
            <style>{tableStyle}</style>
            {contextHolder}
            {renderModal()}
            <PageContainer header={{ breadcrumb: {}, title: "" }} className="no-padding-1">
                {formCols.length > 0 && (
                    <>
                        {renderSearchForm()}
                        <ConfigProvider theme={{ token: { fontSize: 13.5 } }}>
                            <Card className="no-padding" style={{ marginTop: "12px", padding: "12px" }}>
                                {((props.tableButtons && props.tableButtons.length > 0) || (props.downloadPath && props.downloadTableName) || props.tableDesignName || (props.firstTableButtons && props.firstTableButtons.length > 0)) && (
                                    <div style={{ textAlign: "right", marginBottom: "12px" }}>
                                        <Space.Compact>
                                            {props.tableButtons}
                                            {props.tableDesignName && <PageDesign pageName={props.tableDesignName} onCallBack={() => {
                                                // 重新加载列配置
                                                loadColumnConfig();
                                            }} />}
                                            {props.downloadPath && props.downloadTableName && <ExcelDesign tableName={props.downloadTableName} onCallBack={handleExport} />}
                                            {props.firstTableButtons}
                                        </Space.Compact>
                                    </div>
                                )}
                                {renderTable()}
                            </Card>
                        </ConfigProvider>
                    </>
                )}
                {formCols.length === 0 && renderTable()}
            </PageContainer>
        </>
    );
});

export default MyTable;
