import request from "@/utils/request";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { ConfigProvider, Input, message, Modal, PaginationProps, Table, TableProps, Tag } from "antd";
import React, { useImperativeHandle, useState } from "react";

interface Props {
}

type method = {};

const RegisterList = React.forwardRef<method, Props>((props, ref) => {

    const [messageApi, contextHolder] = message.useMessage();

    const [showRegisterInfo, setshowRegisterInfo] = React.useState(false);

    const [registerId, setRegisterId] = useState('');

    useImperativeHandle(ref, () => ({
        open: (RegisterId: string) => {
            setRegisterId(RegisterId);
            setSearchStudentInfoOpen(true);

            search({}, 1, pageSize);
        }
    }));


    const [searchStudentInfoOpen, setSearchStudentInfoOpen] = React.useState(false);

    const [searchKey, setSearchKey] = React.useState('');



    const [searchListData, setSearchListData] = useState<any[]>([]);
    const [searchTotal, setSearchTotal] = useState(0);
    const [searchLoading, setSearchLoading] = useState(false);
    const [current, setCurrent] = useState(1);
    const [pageSize, setPageSize] = useState(15);

    const [searchListParams, setSearchListParams] = React.useState(Object);

    const showTotal: PaginationProps['showTotal'] = (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`;
    const search = (params: any, index: any, size: any) => {
        setSearchListParams(params);
        setSearchLoading(true);

        params.current = index;
        params.pageSize = size;

        setCurrent(index);
        setPageSize(size);

        request<{
            success: boolean;
            message: string;
            data: {
                total: number;
                data: any[];
            };
        }>('/Jx/Student/Student/getStudentList', {
            method: 'POST',
            data: params,
        }).then(json => {
            var studentIds: any[] = [];
            var data = json.data.data;

            data.map((item) => {
                studentIds.push(item.Id);
            });
            request<{
                success: boolean;
                message: string;
                data: string[];
            }>('/Jx/Student/StudentRegister/GetStudentRegisterList', {
                method: 'POST',
                data: {
                    StudentIds: studentIds,
                    RegisterId: registerId == '' ? undefined : registerId,
                },
            }).then(regJson => {

                data.forEach((item) => {
                    if (regJson.data.includes(item.Id)) {
                        item.InDetail = true;
                    } else {
                        item.InDetail = false;
                    }
                });

                setSearchLoading(false);
                setSearchTotal(json.data.total);
                setSearchListData(data);
            })
        })

    }



    const columns: TableProps<any>['columns'] = [
        {
            title: '添加', fixed: 'left', align: 'center', width: 50,
            render: (_, record) => record.InDetail ? <a style={{ color: 'gray' }}>添加</a> :
                <a onClick={() => Modal.confirm({
                    title: '确认操作', icon: <QuestionCircleOutlined />,
                    content: `是否将该学员添加入名册?`, okText: '确认', cancelText: '取消',
                    onOk: async () => {
                        messageApi.loading({ content: '正在操作数据', key: 'loading', duration: 0 });

                        request('/Jx/Student/StudentRegister/addStudentToRegister',
                            {
                                method: 'POST',
                                data: { StudentId: record.Id, RegisterId: registerId }
                            })
                            .then((json) => {
                                messageApi.destroy('loading')
                                if (json?.success) {
                                    messageApi.success(json.message);
                                    search(searchListParams, current, pageSize);
                                }
                            });
                    },
                })}>添加</a>
        },
        {
            title: '移除', fixed: 'left', align: 'center', width: 50,
            render: (_, record) => record.InDetail ? <a onClick={() => Modal.confirm({
                title: '确认操作', icon: <QuestionCircleOutlined />, content: `是否将该学员移除名册?`,
                okText: '确认', cancelText: '取消',
                onOk: async () => {
                    messageApi.loading({ content: '正在操作数据', key: 'loading', duration: 0 });

                    request('/Jx/Student/StudentRegister/removeStudentFromRegister',
                        {
                            method: 'POST',
                            data: { StudentId: record.Id, RegisterId: registerId }
                        })
                        .then((json) => {
                            messageApi.destroy('loading')
                            if (json?.success) {
                                messageApi.success(json.message);
                                search(searchListParams, current, pageSize);
                            }
                        });
                },
            })}>移除</a> : <a style={{ color: 'gray' }}>移除</a>
        },
        { dataIndex: 'RowIndex', title: '序号', align: 'center' },
        { dataIndex: 'xm', title: '名字', align: 'center', render: (_, record) => <a>{record.NoPay > 0 ? <Tag color="red">{record.xm}</Tag> : <Tag>{record.xm}</Tag>}</a> },
        { dataIndex: 'sfzmhm', title: '证件号码', align: 'center' },
        { dataIndex: 'SaleUserName', title: '介绍人', align: 'center' },
        { dataIndex: 'StatusText', title: '状态', align: 'center' },
        { dataIndex: 'Ywzt', title: '业务状态', align: 'center' },
        {
            dataIndex: 'RegistrationDate',
            title: '报名时间',
            align: 'center',
            render: (text: string) => {
                if (!text) return '';
                const date = new Date(text);
                if (date <= new Date('1900-01-02')) return '';
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                }).replace(/\//g, '-');
            }
        },
        {
            dataIndex: 'RegisterTime',
            title: '注册时间',
            align: 'center',
            render: (text: string) => {
                if (!text) return '';
                const date = new Date(text);
                if (date <= new Date('1900-01-02')) return '';
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                }).replace(/\//g, '-');
            }
        },

        { dataIndex: 'CarType', title: '车型', align: 'center' },
        { dataIndex: 'JxClassName', title: '班型', align: 'center' },
        { dataIndex: 'JxDeptName', title: '培训场地', align: 'center' },
        { dataIndex: 'JxFieldName', title: '训练场地', align: 'center' }
    ]
    return (
        <>
            {contextHolder}

            {/* 学员搜索 添加 开始 */}
            <Modal
                onOk={(values: any) => {
                    if (values.Id === '') {
                        values.Id = undefined;
                    }

                    request('/Jx/Student/StudentRegister/setRegisterInfo', {
                        method: 'PUT',
                        data: values,
                    }).then((json) => {
                        if (json && json.success) {
                            setshowRegisterInfo(false);
                            messageApi.success(json.message);
                        }
                    });
                }}
                title={'学员搜索添加'}
                open={searchStudentInfoOpen}
                onCancel={() => {
                    setSearchStudentInfoOpen(false);
                }}
                footer={false}
                width={1000}
                className="no-padding no-padding-1"
            >
                <PageContainer header={{ breadcrumb: {}, title: '' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
                        <div style={{ width: '45%' }}>
                            <Input.Search placeholder="输入关键字搜索" style={{ width: '60%' }} onSearch={(e) => {
                                setSearchKey(e);

                                search({
                                    SearchKey: e
                                }, 1, pageSize);
                            }} enterButton />
                        </div>
                    </div>


                    <ConfigProvider
                        key={'student-list-table-config'}
                        theme={{
                            token: {
                                fontSize: 12.5
                            }
                        }}>
                        <Table<any>
                            key={'user-list-table'}
                            scroll={{ x: 'max-content' }}
                            size={'small'}
                            bordered={false}
                            dataSource={searchListData}
                            loading={searchLoading}
                            pagination={{
                                total: searchTotal, onChange: (page, pageSize) => {
                                    search(searchListParams, page, pageSize);
                                },
                                showTotal: showTotal,
                                pageSize: pageSize,
                                current: current,
                                pageSizeOptions: [15, 30, 100, 200, 500]
                            }}
                            columns={columns}
                        />
                    </ConfigProvider>
                </PageContainer>
            </Modal>
        </>
    );
});

export default RegisterList;