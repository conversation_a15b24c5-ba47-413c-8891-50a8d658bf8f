import MyTable, { MyTableColumnProps } from "@/components/CustomComponent/MyTable/index";
import { PageContainer } from "@ant-design/pro-components";
import { Button, Modal, Image, message, Tag } from "antd";
import { LinkOutlined, SettingOutlined, BarsOutlined } from "@ant-design/icons";
import React, { useRef, useState } from "react";
import { FormItem } from "@/components/CustomComponent/DynamicFormBuilder";
import { request } from "@/utils/request";
import MyFormModal from "@/components/CustomComponent/MyFormModal";
import JxStudent from "@/components/JiaXiao/Student";
import RefundDetailModal from './RefundDetailModal';
import AccountInfoCard from '@/components/CustomComponent/AccountInfoCard';
import BatchOperationModal, { BatchOperationModalRef } from './BatchOperationModal';
import { getComputerAccountSelectList, getCostTypeSelectList } from "@/services/select/pay";
import { getJxClassSelectList } from "@/services/select/jiaXiao";

const WxRefundAudit: React.FC = () => {
    const tableRef = useRef<any>();
    const priceConfigModalRef = useRef<any>();
    const studentInfoRef: any = useRef(null);
    const batchOperationModalRef = useRef<BatchOperationModalRef>(null);
    const [qrCodeVisible, setQrCodeVisible] = useState(false);
    const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
    const [loading, setLoading] = useState(false);
    const [detailVisible, setDetailVisible] = useState(false);
    const [detailData, setDetailData] = useState<any>(null);
    const [data, setData] = useState<any>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [currentOperation, setCurrentOperation] = useState<'approve' | 'reject' | 'offline' | 'wechat'>();

    const formItems: FormItem[] = [
        {
            name: "SearchKey",
            type: "input",
            label: "关键字词",
            placeholder: "请输入名字证件号关键字",
            required: false
        },
        {
            name: "CreateTimes",
            type: "daterange",
            label: "申请时间",
            placeholder: "请选择时间范围",
            required: false
        },
        {
            name: "ksrqs",
            type: "daterange",
            label: "考试日期",
            placeholder: "请选择考试日期范围",
            required: false
        },
        {
            name: "KeMuIds",
            type: "select",
            label: "考试科目",
            placeholder: "请选择考试科目",
            required: false,
            multiple: true,
            options: [
                { label: "科目一", value: 1 },
                { label: "科目二", value: 2 },
                { label: "科目三", value: 3 },
                { label: "科目四", value: 4 }
            ]
        },
        {
            name: "AuditStatus",
            type: "select",
            label: "审核状态",
            placeholder: "请选择审核状态",
            required: false,
            multiple: true,
            options: [
                { label: "待审核", value: 0 },
                { label: "已通过", value: 1 },
                { label: "已拒绝", value: 2 }
            ]
        },
        {
            name: "IsPaids",
            type: "select",
            label: "退款状态",
            placeholder: "请选择退款状态",
            required: false,
            multiple: true,
            options: [
                { label: "未退款", value: 0 },
                { label: "已退款", value: 1 },
            ]
        }
    ];

    const priceConfigFormItems: FormItem[] = [
        {
            name: "refundMethodAlert",
            type: "alert",
            label: "退款方式说明",
            description: "1. 银行转账：学员需提供银行卡信息，驾校审核后导出列表，通过银行后台进行批量转账。2. 微信红包：驾校需将款项转入盼达软件指定账户，系统完成充值后，可直接批量发放至学员微信。注：微信实名需与学员姓名一致。",
            alertType: "info",
            showIcon: true,
        },
        {
            name: "RefundMethod",
            type: "select",
            label: "退款方式",
            placeholder: "请选择退款方式",
            required: true,
            options: [
                { label: "银行转账", value: "BankTransfer" },
                { label: "微信红包", value: "WeChatRedPacket" }
            ]
        },
        {
            name: "ComputerAccountId",
            type: "select",
            label: "结算账户",
            placeholder: "请选择结算账户",
            request: getComputerAccountSelectList
        },
        {
            name: "JxClassIds",
            type: "select",
            label: "可退班别",
            placeholder: "请选择班别，可为空，为空则所有班别都可退",
            multiple: true,
            request: getJxClassSelectList
        },
        {
            name: "BankScreenshotCount",
            type: "number",
            label: "银行转账截图数量",
            placeholder: "请输入银行转账截图数量",
            required: true,
        },
        {
            name: "BankScreenPDFCount",
            type: "number",
            label: "银行转账PDF数量",
            placeholder: "请输入银行转账PDF数量",
            required: true,
        },
        {
            name: "Subject1FirstExamRefund",
            type: "number",
            label: "科一初考价格",
            placeholder: "请输入科一初考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科一初考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject1FirstExamRefundCostTypeId",
            type: "select",
            label: "科一初考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        },
        {
            name: "Subject1RetakeExamRefund",
            type: "number",
            label: "科一补考价格",
            placeholder: "请输入科一补考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科一补考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject1RetakeExamRefundCostTypeId",
            type: "select",
            label: "科一补考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        },
        {
            name: "Subject2FirstExamRefund",
            type: "number",
            label: "科二初考价格",
            placeholder: "请输入科二初考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科二初考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject2FirstExamRefundCostTypeId",
            type: "select",
            label: "科二初考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        },
        {
            name: "Subject2RetakeExamRefund",
            type: "number",
            label: "科二补考价格",
            placeholder: "请输入科二补考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科二补考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject2RetakeExamRefundCostTypeId",
            type: "select",
            label: "科二补考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        },
        {
            name: "Subject3FirstExamRefund",
            type: "number",
            label: "科三初考价格",
            placeholder: "请输入科三初考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科三初考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject3FirstExamRefundCostTypeId",
            type: "select",
            label: "科三初考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        },
        {
            name: "Subject3RetakeExamRefund",
            type: "number",
            label: "科三补考价格",
            placeholder: "请输入科三补考价格，如果不退写0",
            required: true,
            rules: [
                { required: true, message: "请输入科三补考价格" },
                { type: "number", min: 0, message: "价格必须大于等于0" }
            ]
        },
        {
            name: "Subject3RetakeExamRefundCostTypeId",
            type: "select",
            label: "科三补考挂账支出费用类型",
            placeholder: "请选择关联类型",
            search: true,
            request: getCostTypeSelectList
        }
    ];

    const showDetail = (record: any) => (
        <a
            type="link"
            onClick={() => {
                setDetailData(record);
                setDetailVisible(true);
            }}
        >
            <BarsOutlined />
        </a>
    );

    const showInfo = (record: any) => (
        <a
            key="edit"
            onClick={() => {
                studentInfoRef?.current?.GetStudentInfo(record.StudentId);
            }}
            title="详情"
        >
            {record.xm}
        </a>
    );

    const columns: MyTableColumnProps[] = [
        { dataIndex: "RowIndex", title: "序号", align: "center" },
        {
            dataIndex: "detail",
            title: "详情",
            align: "center",
            render: (_: any, record: any) => showDetail(record)
        },
        { dataIndex: "xm", title: "姓名", align: "center", render: (dom: any, record: any) => showInfo(record) },
        {
            dataIndex: "StatusText",
            title: "状态",
            align: "center",
            render: (text: string, record: any) => {
                let color = '';
                switch (record.Status) {
                    case 0: // WaitAudit
                        color = 'processing'; // blue
                        break;
                    case 1: // AuditPass
                        color = 'success'; // green
                        break;
                    case 2: // AuditFail
                        color = 'error'; // red
                        break;
                    default:
                        color = 'default';
                }
                return <Tag color={color}>{text}</Tag>;
            }
        },
        { dataIndex: "RefundAmount", title: "退款金额", align: "center" },
        { dataIndex: "KeMuText", title: "科目", align: "center" },
        { dataIndex: "ksrq", title: "考试日期", align: "center", IsDate: true },
        { dataIndex: "AuditorName", title: "审核人", align: "center" },
        { dataIndex: "AuditTime", title: "审核时间", align: "center", IsTime: true },
        { dataIndex: "AuditRemark", title: "审核备注", align: "center" },
        {
            dataIndex: "IsPaid", title: "退款状态", align: "center", render: (text: string, record: any) => {
                if (text == "1") {
                    return <Tag color="success">已退款</Tag>;
                } else {
                    return <Tag color="default">未退款</Tag>;
                }
            }
        },
        { dataIndex: "RefundTime", title: "退款时间", align: "center", IsTime: true },
        { dataIndex: "RefundRemark", title: "退款备注", align: "center" },
        {
            dataIndex: "sfzmhm", title: "身份证号", align: "center", render: (text: string) => {
                if (!text) return '-';
                // Keep first 6 and last 4 digits, mask the rest with *
                return text.replace(/^(.{6})(?:\d+)(.{4})$/, '$1********$2');
            }
        },
        { dataIndex: "RegistrationDate", title: "报名日期", align: "center", IsDate: true },
        { dataIndex: "Ywzt", title: "业务状态", align: "center" },
        { dataIndex: "JxDeptName", title: "报名点", align: "center" },
        { dataIndex: "JxClassName", title: "班型", align: "center" },
        { dataIndex: "CarType", title: "车型", align: "center" },
        { dataIndex: "SaleUserName", title: "推荐人", align: "center" },
        { dataIndex: "TeachOneUserName", title: "科一教练", align: "center" },
        { dataIndex: "TeachTwoUserName", title: "科二教练", align: "center" },
        { dataIndex: "TeachThreeUserName", title: "科三教练", align: "center" },
        { dataIndex: "kcmc", title: "考场名称", align: "center" },
        { dataIndex: "kscc", title: "考试场次", align: "center" },
        { dataIndex: "zjcx", title: "考试车型", align: "center" },
        { dataIndex: "lsh", title: "流水号", align: "center" },
        { dataIndex: "Remark", title: "备注", align: "center" },
        { dataIndex: "BankCardNumber", title: "银行卡号", align: "center" },
        { dataIndex: "BankName", title: "银行名称", align: "center" },
        { dataIndex: "BankBranchName", title: "支行名称", align: "center" },
        { dataIndex: "BankAccountName", title: "开户人姓名", align: "center" },
        { dataIndex: "CreateTime", title: "创建时间", align: "center", IsTime: true },
        { dataIndex: "CreateUserName", title: "创建人", align: "center" },
        { dataIndex: "RefundOperatorName", title: "退款操作人", align: "center" },
        { dataIndex: "RefundNo", title: "退款单号", align: "center" },
        { dataIndex: "OpenId", title: "微信OpenId", align: "center" },
    ];

    const handleAudit = (record: any) => {
        // TODO: Implement audit logic
        console.log('Audit record:', record);
    };

    const handlePriceConfig = () => {
        priceConfigModalRef.current?.open('********-0000-0000-0000-************');
    };

    const handleGenerateQRCode = async () => {
        setLoading(true);
        try {
            const response = await request(`/Jx/Student/Audit/WxRefundAudit/generateQRCode`, {
                method: "POST",
                data: {
                }
            });
            if (response.data) {
                let qrCodeData = response.data;
                // 检查是否已经包含base64头部
                if (!qrCodeData.startsWith('data:image/')) {
                    qrCodeData = `data:image/png;base64,${qrCodeData}`;
                }
                setQrCodeUrl(qrCodeData);
                setQrCodeVisible(true);
            }
        } catch (error) {
            console.error('Failed to generate QR code:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleBatchOperation = (operationType: 'approve' | 'reject' | 'offline' | 'wechat') => {
        if (selectedRowKeys.length === 0) {
            message.warning('请选择要处理的记录');
            return;
        }
        setCurrentOperation(operationType);
        batchOperationModalRef.current?.startOperation(operationType);
    };

    const handleBatchSuccess = () => {
        tableRef.current?.reload();
        setSelectedRowKeys([]);
    };

    const handleRepayment = async (record: any) => {
        try {
            message.loading('重新付款中...');
            const response = await request('/Jx/Student/Audit/WxRefundAuditPay/wechatPay', {
                method: 'POST',
                data: {
                    id: record.Id
                }
            });
            message.destroy();

            if (response.success) {
                message.success('重新付款成功');
                tableRef.current?.reload();
            }
            else {
                message.error(response.message || '重新付款失败');
            }
        } catch (error) {
            console.error('Repayment failed:', error);
            message.error('重新付款失败，请稍后重试');
        } finally {
        }
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
    };

    return (
        <PageContainer header={{ breadcrumb: {}, title: "" }}>
            <AccountInfoCard showWxRefundCouponDetail={true} onRepayment={handleRepayment} />
            <MyTable
                key="wx-refund-audit-table"
                ref={tableRef}
                formCols={formItems}
                columns={columns}
                getPath="/Jx/Student/Audit/WxRefundAudit/page"
                rowKey="Id"
                downloadPath="/Jx/Student/Audit/WxRefundAudit/export"
                downloadTableName="WxRefundAudit"
                rowSelection={rowSelection}
                batchOperations={[
                    {
                        key: 'approve',
                        label: '批量审核通过',
                        onClick: () => handleBatchOperation('approve')
                    },
                    {
                        key: 'reject',
                        label: '批量审核驳回',
                        onClick: () => handleBatchOperation('reject')
                    },
                    {
                        key: 'offline',
                        label: '批量线下返现',
                        onClick: () => handleBatchOperation('offline')
                    },
                    {
                        key: 'wechat',
                        label: '批量微信红包',
                        onClick: () => handleBatchOperation('wechat')
                    }
                ]}
                onLoadCallback={(params, response) => {
                    if (response?.data?.data) {
                        setData(response.data.data);
                    }
                }}
                showBalance={async (response) => {
                    const summary = response?.data?.summary || data?.summary;
                    if (!summary) return '';
                    return `总金额：${summary.TotalAmount?.toFixed(2) || '0.00'} 元，已退款金额：${summary.PaidAmount?.toFixed(2) || '0.00'} 元，已审核未退款金额：${summary.ApprovedUnpaidAmount?.toFixed(2) || '0.00'} 元，待审核金额：${summary.PendingAmount?.toFixed(2) || '0.00'} 元`;
                }}
                tableButtons={[
                    <Button
                        key="price-config"
                        type="primary"
                        ghost
                        icon={<SettingOutlined />}
                        onClick={handlePriceConfig}
                    >
                        价格配置
                    </Button>,
                    <Button
                        key="generate-qr"
                        type="primary"
                        ghost
                        icon={<LinkOutlined />}
                        onClick={handleGenerateQRCode}
                        loading={loading}
                    >
                        申请链接
                    </Button>
                ]}

            />
            <BatchOperationModal
                ref={batchOperationModalRef}
                onCancel={() => setSelectedRowKeys([])}
                selectedIds={selectedRowKeys}
                onSuccess={handleBatchSuccess}
            />
            <Modal
                title="申请二维码"
                open={qrCodeVisible}
                onCancel={() => setQrCodeVisible(false)}
                footer={null}
            >
                {qrCodeUrl && (
                    <div style={{ textAlign: 'center' }}>
                        <Image src={qrCodeUrl} alt="QR Code" />
                    </div>
                )}
            </Modal>
            <RefundDetailModal
                visible={detailVisible}
                onCancel={() => setDetailVisible(false)}
                data={detailData}
            />
            <MyFormModal
                ref={priceConfigModalRef}
                formItems={priceConfigFormItems}
                insertTitle="价格配置"
                modifyTitle="价格配置"
                getPath="/Config/JxRefundConfig/getRefundConfig"
                setPath="/Config/JxRefundConfig/setRefundConfig"
                width={600}
            />
            <JxStudent ref={studentInfoRef} StudentListRef={undefined} updateAddLoading={undefined} />
        </PageContainer>
    );
};

export default WxRefundAudit;