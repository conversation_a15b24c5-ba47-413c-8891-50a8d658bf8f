import { FormItem, FormItemType } from "@/components/CustomComponent/DynamicFormBuilder/index";
import {
    getCarTypeSelectList,
    getImformationStatusSelectList,
    getJxClassSelectList,
    getJxDeptSelectList,
    getJxFieldSelectList,
    getRegisterSelectList,
    getSourceSelectList,
    getStatusSelectList,
} from "@/services/select/jiaXiao";
import { getCostTypeSelectList } from "@/services/select/pay";
import { getCategoryDetailTreeList } from "@/services/select/sys";

export const DEFAULT_PAGE_SIZE = 15;

export const RESULT_ID_SELECT_LIST = [
    { value: "0", label: "未考" },
    { value: "2", label: "合格" },
    { value: "3", label: "不合格" },
    { value: "4", label: "缺考" },
    { value: "5", label: "仪器故障" },
    { value: "6", label: "取消" },
    { value: "7", label: "待考" },
];
const examResultsOptions = [
    { value: "0", label: "未考" },
    { value: "2", label: "合格" },
    { value: "3", label: "不合格" },
    { value: "4", label: "缺考" },
    { value: "5", label: "仪器故障" },
    { value: "6", label: "取消" },
    { value: "7", label: "待考" },
];

export const DEFAULT_SEARCH_ITEMS: SearchItemType = {
    SearchKey: { Checked: true, Name: "关键字词", props: { type: "input", placeholder: "姓名、身份证号、电话、支持批量搜索" } },
    xm: { Checked: false, Name: "学员姓名", props: { type: "input" } },
    sfzmhm: { Checked: false, Name: "证件号码", props: { type: "input" } },
    sfzmhm_Start: { Checked: false, Name: "证件开头", props: { type: "input" } },
    sfzmhm_End: { Checked: false, Name: "证件结尾", props: { type: "input" } },
    yddh: { Checked: false, Name: "手机号码", props: { type: "input" } },
    Times: { Checked: true, Name: "时间搜索" },
    Times2: { Checked: false, Name: "时间搜索" },
    Statuss: { Checked: true, Name: "学员状态", props: { type: "select", request: getStatusSelectList, multiple: true, maxTagCount: 1 } },
    CostTypeIds: { Checked: true, Name: "缴费包括", props: { type: "select", request: getCostTypeSelectList, multiple: true, maxTagCount: 1 } },
    RegisterSchoolName: { Checked: false, Name: "注册驾校", props: { type: "input" } },

    KeMu1ResultIds: { Checked: false, Name: "科一成绩", props: { type: "select", options: examResultsOptions, multiple: true, maxTagCount: 1 } },
    KeMu1ExamDates: { Checked: false, Name: "科一日期", props: { type: "daterange" } },
    KeMu2ResultIds: { Checked: false, Name: "科二成绩", props: { type: "select", options: examResultsOptions, multiple: true, maxTagCount: 1 } },
    KeMu2ExamDates: { Checked: false, Name: "科二日期", props: { type: "daterange" } },
    KeMu3ResultIds: { Checked: false, Name: "科三成绩", props: { type: "select", options: examResultsOptions, multiple: true, maxTagCount: 1 } },
    KeMu3ExamDates: { Checked: false, Name: "科三日期", props: { type: "daterange" } },
    KeMu4ResultIds: { Checked: false, Name: "科四成绩", props: { type: "select", options: examResultsOptions, multiple: true, maxTagCount: 1 } },
    KeMu4ExamDates: { Checked: false, Name: "科四日期", props: { type: "daterange" } },

    SaleUserIds: { Checked: false, Name: "推荐人员", props: { type: "selectusers" } },
    SaleUserId2s: { Checked: false, Name: "协单人员", props: { type: "selectusers" } },
    SaleUserId3s: { Checked: false, Name: "责任人员", props: { type: "selectusers" } },
    TeachOneUserIds: { Checked: false, Name: "科一教练", props: { type: "selectusers" } },
    TeachTwoUserIds: { Checked: false, Name: "科二教练", props: { type: "selectusers" } },
    TeachThreeUserIds: { Checked: false, Name: "科三教练", props: { type: "selectusers" } },
    TeachUserIds: { Checked: false, Name: "任何教练", props: { type: "selectusers" } },
    SaleOrTeachUserIds: { Checked: false, Name: "推荐&教练", props: { type: "selectusers" } },

    JxDeptIds: { Checked: false, Name: "报名门店", props: { type: "select", request: getJxDeptSelectList, multiple: true, maxTagCount: 1 } },
    SaleJxDeptIds: { Checked: false, Name: "报名校区", props: { type: "select", request: getJxDeptSelectList, multiple: true, maxTagCount: 1 } },
    CurrentSaleJxDeptIds: { Checked: false, Name: "当前校区", props: { type: "select", request: getJxDeptSelectList, multiple: true, maxTagCount: 1 } },
    JxClassIds: { Checked: false, Name: "报名班别", props: { type: "select", request: getJxClassSelectList, multiple: true, maxTagCount: 1 } },
    JxFieldIds: { Checked: false, Name: "培训场地", props: { type: "select", request: getJxFieldSelectList, multiple: true, maxTagCount: 1 } },
    CarTypes: { Checked: false, Name: "培训车型", props: { type: "select", request: getCarTypeSelectList, multiple: true, maxTagCount: 1 } },
    RegisterId: { Checked: false, Name: "注册名册", props: { type: "select", request: getRegisterSelectList } },
    JxStudentImformationStatusIds: { Checked: false, Name: "资料状态", props: { type: "select", request: getImformationStatusSelectList, multiple: true, maxTagCount: 1 } },

    NoPays: { Checked: false, Name: "总额欠费", props: { type: "numbers" } },
    TuitionNoPays: { Checked: false, Name: "学费欠费", props: { type: "numbers" } },
    TuitionPays: { Checked: false, Name: "学费实缴", props: { type: "numbers" } },

    KeMu2Times: { Checked: false, Name: "科二次数", props: { type: "numbers" } },
    KeMu3Times: { Checked: false, Name: "科三次数", props: { type: "numbers" } },

    Ages: { Checked: false, Name: "学员年龄", props: { type: "numbers" } },

    SourceIds: { Checked: false, Name: "学员来源", props: { type: "select", request: getSourceSelectList, multiple: true, maxTagCount: 1 } },

    SaleCategoryIds: { Checked: false, Name: "人员分类", props: { type: "select", request: getCategoryDetailTreeList, multiple: true, maxTagCount: 1 } },
    Remark: { Checked: false, Name: "学员备注", props: { type: "input" } },
    MyStudentColumn: { Checked: false, Name: "自定字段", props: { type: "input" } },
    NoPhotos: {
        Checked: false, Name: "未拍照片", props: {
            type: "select",
            options: [
                { label: "现场拍照", value: 2 },
                { label: "寸照", value: 0 },
                { label: "身份证明正反面", value: 4 },
                { label: "驾驶证申请表", value: 6 },
                { label: "合同", value: 201 },
            ], multiple: true, maxTagCount: 1
        }
    },
    HavePhotos: {
        Checked: false, Name: "已拍照片", props: {
            type: "select",
            options: [
                { label: "现场拍照", value: 2 },
                { label: "寸照", value: 0 },
                { label: "身份证明正反面", value: 4 },
                { label: "驾驶证申请表", value: 6 },
                { label: "合同", value: 201 },
            ], multiple: true, maxTagCount: 1
        }
    },
};

export const TIME_TYPE_OPTIONS = [
    { label: `录入时间`, value: "CreateTime" },
    { label: `报名时间`, value: "RegistrationDate" },
    { label: `缴费时间`, value: "PayTime" },
    { label: `完费时间`, value: "PayCompleteTime" },
    { label: `受理日期`, value: "RegisterTime" },
    { label: `退学日期`, value: "DropOutTime" },
    { label: `出生日期`, value: "csrq" },
];

export const getBatchMenuItems = (currentUser?: any) => [
    { label: "同步人脸信息", key: "1" },
    { label: "同步学员状态", key: "2" },
    { label: "批量发送短信", key: "3" },
    { label: "批量计时提交", key: "4", hidden: !currentUser?.cityId?.toString().startsWith('4301') }
];

// export const BATCH_MENU_ITEMS = [
//     { label: "同步人脸信息", key: "1" },
//     { label: "同步学员状态", key: "2" },
//     { label: "批量发送短信", key: "3" },
//     { label: "批量计时提交", key: "4" }
// ];

export const IMPORT_MENU_ITEMS = [
    { label: "导入数据", key: "1" },
    { label: "下载模板", key: "2" },
    { label: "其他同步", key: "3" },
];

export const DEFAULT_DATE_FORMAT = "YYYY-MM-DD";

export const DEFAULT_TIME_RANGE = {
    start: "-15d",
    end: "now",
};

export interface SearchItemType {
    [key: string]: {
        Checked: boolean; // 是否选中/显示
        Name: string; // 显示名称
        props?: FormItem;
    };
}

export function convertSearchItemToFormItems(searchItems: SearchItemType): FormItem[] {
    let items: FormItem[] = [];
    Object.entries(searchItems).forEach(([key, value]) => {
        if (value.Checked) {
            if (key == "Times") {
                items.push({
                    name: "TimeType",
                    type: "select",
                    label: value.Name,
                    placeholder: "请选择时间区间类型",
                    options: TIME_TYPE_OPTIONS,
                    tooltip: "必须选择时间类型，否则选择的时间区间将无效",
                });
                items.push({
                    name: "Times",
                    type: "daterange",
                    label: "时间区间",
                });
            } else if (key == "Times2") {
                items.push({
                    name: "TimeType2",
                    type: "select",
                    label: value.Name,
                    placeholder: "请选择时间区间类型",
                    options: TIME_TYPE_OPTIONS,
                    tooltip: "必须选择时间类型，否则选择的时间区间将无效",
                });
                items.push({
                    name: "Times2",
                    type: "daterange",
                    label: "时间区间",
                });
            } else {
                try {
                    const formItem: FormItem = DEFAULT_SEARCH_ITEMS[key] ? {
                        ...DEFAULT_SEARCH_ITEMS[key].props,
                        name: key,
                        allowClear: true,
                        label: value.Name,
                        ...value.props,
                    } : {
                        ...searchItems[key].props,
                        name: key,
                        allowClear: true,
                        label: value.Name,
                        ...value.props,
                    };

                    // Set placeholder based on input type
                    if (value.props?.type === "input" || value.props?.type === "numbers" || value.props?.type === "number") {
                        formItem.placeholder = `请输入${value.Name}`;
                    } else {
                        formItem.placeholder = `请选择${value.Name}`;
                    }

                    items.push(formItem);
                } catch (error) {
                    console.log('error', key);
                    console.log(error);
                }
            }
        }
    });

    return items;
}
