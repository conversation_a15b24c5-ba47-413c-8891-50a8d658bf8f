import {
    getCarTypeSelectList,
    getJxClassSelectList,
    getJxDeptSelectList,
    getJxFieldSelectList,
} from '@/services/select/jiaXiao';
import { getUserSelectList } from '@/services/select/sys';
import request from '@/utils/request';
import { DeleteOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import React from 'react';
import MyTable, { TableMethods } from '@/components/CustomComponent/MyTable';
import MyFormModal from '@/components/CustomComponent/MyFormModal';
import { FormItem } from '@/components/CustomComponent/DynamicFormBuilder';

type configItem = {
    Id: any;
    Name: any;
    SortCode: number;
    JxDeptIds: string[];
    JxFieldIds: string[];
    JxClassIds: string[];
    SaleUserIds: string[];
    TeachTwoUserIds: string[];
    TeachThreeUserIds: string[];
};

const StudyList: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();

    const configListRef = React.useRef<TableMethods | null>(null);

    /**
     * 配置的表单
     */
    const [showConfigInfo, setShowConfigInfo] = React.useState(false);
    const [configInfo, setConfigInfo] = React.useState<configItem>();
    const configInfoRef = React.useRef<any>();

    // 表单配置项
    const formItems: FormItem[] = [
        {
            name: "Id",
            type: "hidden"
        },
        {
            name: "Name",
            type: "input",
            label: "配置标题",
            required: true,
            placeholder: "请输入当前配置的标题",
            width: "xl"
        },
        {
            type: "divider",
            label: "配置匹配条件"
        },
        {
            name: "JxDeptIds",
            type: "select",
            label: "报名门店",
            placeholder: "选择关联的报名门店",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getJxDeptSelectList(
                    params?.SearchKey,
                    configInfo?.JxDeptIds ? { JxDeptIds: configInfo.JxDeptIds.join(',') } : undefined
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "JxClassIds",
            type: "select",
            label: "报名班型",
            placeholder: "选择关联的报名班型",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getJxClassSelectList(
                    params?.SearchKey,
                    configInfo?.JxClassIds
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "JxFieldIds",
            type: "select",
            label: "训练场地",
            placeholder: "选择关联的训练场地",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getJxFieldSelectList(
                    params?.SearchKey,
                    configInfo?.JxFieldIds ? { JxFieldIds: configInfo.JxFieldIds.join(',') } : undefined
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "CarTypes",
            type: "select",
            label: "准驾车型",
            placeholder: "选择关联的准驾车型",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async () => {
                const json = await getCarTypeSelectList();
                return json.success ? json.data : [];
            }
        },
        {
            name: "SaleUserIds",
            type: "select",
            label: "推荐人员",
            placeholder: "选择业务推荐人员",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getUserSelectList(
                    params?.SearchKey,
                    configInfo?.SaleUserIds ? { SaleUserIds: configInfo.SaleUserIds.join(',') } : undefined
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "TeachTwoUserIds",
            type: "select",
            label: "科二教练",
            placeholder: "选择科二教练",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getUserSelectList(
                    params?.SearchKey,
                    configInfo?.TeachTwoUserIds ? { TeachTwoUserIds: configInfo.TeachTwoUserIds.join(',') } : undefined
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "TeachThreeUserIds",
            type: "select",
            label: "科三教练",
            placeholder: "选择科三教练",
            multiple: true,
            maxTagCount: 0,
            width: "sm",
            request: async (params: any) => {
                const json = await getUserSelectList(
                    params?.SearchKey,
                    configInfo?.TeachThreeUserIds ? { TeachThreeUserIds: configInfo.TeachThreeUserIds.join(',') } : undefined
                );
                return json.success ? (json.data || []) : [];
            }
        },
        {
            name: "RegistrationDates",
            type: "daterange",
            label: "报名日期",
            width: "xl"
        },
        {
            type: "divider",
            label: "配置详细信息"
        },
        {
            name: "SortCode",
            type: "number",
            label: "优先级别",
            required: true,
            placeholder: "请输入正确的优先级别",
            width: "sm"
        },
        {
            name: "OrderAfterSubject",
            type: "select",
            label: "预约条件",
            placeholder: "选择可以开始预约的条件",
            required: true,
            width: "sm",
            options: [
                { label: '报名成功', value: 0 },
                { label: '注册成功', value: 1 },
                { label: '科一合格', value: 2 }
            ]
        },
        {
            name: "OrderCarShowTimeType",
            type: "select",
            label: "显示场次",
            placeholder: "选择显示场次计算类型",
            required: true,
            width: "sm",
            options: [
                { label: '当日的零点开始', value: 0 },
                { label: '当前时间开始', value: 1 }
            ]
        },
        {
            name: "OrderCarShowTime",
            type: "number",
            label: "范围数值",
            placeholder: "单位为小时",
            required: true,
            width: "sm"
        },
        {
            name: "OrderCarEndTimeType",
            type: "select",
            label: "结束预约",
            placeholder: "选择结束预约计算类型",
            required: true,
            width: "sm",
            options: [
                { label: '训练当天零点前', value: 0 },
                { label: '训练场次开始前', value: 1 }
            ]
        },
        {
            name: "OrderCarEndTime",
            type: "number",
            label: "范围数值",
            placeholder: "单位为小时",
            required: true,
            width: "sm"
        },
        {
            name: "OrderCarLastCancleType",
            type: "select",
            label: "取消时间",
            placeholder: "选择取消时间计算类型",
            required: true,
            width: "sm",
            options: [
                { label: '训练当天零点前', value: 0 },
                { label: '训练场次开始前', value: 1 }
            ]
        },
        {
            name: "OrderCarLastCancleTime",
            type: "number",
            label: "范围数值",
            placeholder: "单位为小时",
            required: true,
            width: "sm"
        },
        {
            type: "divider",
            label: "科目二相关配置"
        },
        {
            name: "ChooseTeachTwoTimes",
            type: "number",
            label: "科目二自选教练场次数",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachTwoTimesDaily",
            type: "number",
            label: "科目二一天可预约场次",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachTwoTimesCount",
            type: "number",
            label: "科目二总预约成功场次",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachTwoTrainFeeLimit",
            type: "number",
            label: "科目二学费总欠费上限",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachTwoFeeLimit",
            type: "number",
            label: "科目二总费用欠费上限",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            type: "divider",
            label: "科目三相关配置"
        },
        {
            name: "ChooseTeachThreeTimes",
            type: "number",
            label: "科目三自选教练场次数",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachThreeTimesDaily",
            type: "number",
            label: "科目三一天可预约场次",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachThreeTimesCount",
            type: "number",
            label: "科目三总预约成功场次",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachThreeTrainFeeLimit",
            type: "number",
            label: "科目三学费总欠费上限",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        },
        {
            name: "OrderTeachThreeFeeLimit",
            type: "number",
            label: "科目三总费用欠费上限",
            placeholder: "请直接输入数字",
            required: true,
            width: "sm"
        }
    ];

    /**
     *
     * @param id
     */
    const getOrderConfigData = (id: any) => {
        if (id === undefined) {
            setConfigInfo({
                Id: undefined,
                Name: '',
                SortCode: 0,
                JxDeptIds: [],
                JxFieldIds: [],
                JxClassIds: [],
                SaleUserIds: [],
                TeachTwoUserIds: [],
                TeachThreeUserIds: [],
            });
            setShowConfigInfo(true);
        } else {
            messageApi.loading({ content: '正在加载信息', key: 'loading', duration: 0 });
            request('/JiaXiao/OrderCar/OrderConfig/getConfigInfo', {
                method: 'POST',
                data: {
                    Id: id,
                },
            }).then((json) => {
                messageApi.destroy('loading');
                if (json && json.success) {
                    setConfigInfo(json.data);
                    setShowConfigInfo(true);
                }
            });
        }
    };
    return (
        <>
            {contextHolder}
            <PageContainer
                header={{
                    breadcrumb: {},
                    title: '',
                }}
            >
                <MyTable
                    ref={configListRef}
                    rowKey="Id"
                    getPath="/JiaXiao/OrderCar/OrderConfig/getConfigList"
                    columns={[
                        {
                            dataIndex: 'Index',
                            title: '序号',
                            fixed: 'left',
                            align: 'center',
                            width: 50,
                        },
                        {
                            dataIndex: 'Name',
                            title: '配置名称',
                            align: 'center',
                            ellipsis: true,
                            render: (_, record) => [
                                <a
                                    key="edit"
                                    onClick={() => {
                                        getOrderConfigData(record.Id);
                                    }}
                                    title="编辑"
                                >
                                    {record.Name}
                                </a>,
                            ],
                        },
                        {
                            title: '删除',
                            align: 'center',
                            ellipsis: true,
                            width: 50,
                            render: (_, record) => [
                                <a
                                    key={'delete_Config_info'}
                                    onClick={() => {
                                        Modal.confirm({
                                            title: '确认操作',
                                            icon: <QuestionCircleOutlined />,
                                            content: '确认删除当前的配置信息?',
                                            onOk: async () => {
                                                await request(
                                                    '/JiaXiao/OrderCar/OrderConfig/deleteConfigInfo',
                                                    {
                                                        method: 'DELETE',
                                                        data: {
                                                            Id: record.Id,
                                                        },
                                                    },
                                                ).then((json) => {
                                                    if (json && json.success) {
                                                        message.success(json.message);
                                                        configListRef.current?.reload();
                                                    }
                                                });
                                            },
                                            onCancel: async () => { },
                                        });
                                    }}
                                >
                                    <DeleteOutlined />
                                </a>,
                            ],
                        },
                        {
                            title: '创建人',
                            dataIndex: 'CreateUserName',
                            align: 'center',
                            ellipsis: true,
                        },
                        {
                            title: '创建时间',
                            dataIndex: 'CreateTime',
                            align: 'center',
                            ellipsis: true,
                        },
                    ]}
                    tableButtons={[
                        <Button
                            key="add"
                            type="primary"
                            icon={<PlusCircleOutlined />}
                            onClick={() => {
                                getOrderConfigData(undefined);
                            }}
                        >
                            添加配置
                        </Button>,
                    ]}
                />

                {/*  预约配置编辑  开始  */}
                <MyFormModal
                    ref={configInfoRef}
                    formItems={formItems}
                    modifyTitle="约车配置编辑"
                    insertTitle="约车配置新增"
                    getPath=""
                    setPath="/JiaXiao/OrderCar/OrderConfig/setConfigInfo"
                    width={1100}
                    open={showConfigInfo}
                    onOpenChange={setShowConfigInfo}
                    idOrData={configInfo}
                    onCallBack={(response) => {
                        if (response && response.success) {
                            configListRef.current?.reload();
                        }
                    }}
                />
            </PageContainer>
        </>
    );
};

export default StudyList;
