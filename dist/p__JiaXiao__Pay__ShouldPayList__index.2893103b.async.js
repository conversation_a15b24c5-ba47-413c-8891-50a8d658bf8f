"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9587],{95641:function(he,$,e){var G=e(15009),i=e.n(G),H=e(97857),T=e.n(H),Q=e(99289),_=e.n(Q),C=e(5574),L=e.n(C),W=e(78158),J=e(18922),V=e(85576),A=e(83622),Z=e(71230),X=e(15746),Y=e(84567),N=e(67294),K=e(45360),m=e(85893),q=N.forwardRef(function(B,le){var w=B.pageName,s=B.searchItems,R=B.onCallBack,ee=K.ZP.useMessage(),z=L()(ee,2),ae=z[0],te=z[1],ne=(0,N.useState)(!1),U=L()(ne,2),d=U[0],P=U[1],h=J.Z.useForm(),D=L()(h,1),f=D[0],j=(0,N.useState)(!1),M=L()(j,2),E=M[0],g=M[1],v=function(){var t=_()(i()().mark(function l(){var a,r,c,o,x;return i()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.prev=0,S.next=3,(0,W.ZP)("/SystemManage/UserSearch/getConfigJson",{method:"POST",data:{PageName:w},errorMessage:!1});case 3:a=S.sent,a.success&&a.data==""?(r=T()({},s),R&&R(r)):a.success&&a.data&&(c=typeof a.data=="string"?JSON.parse(a.data):a.data,o=c.ConfigJson?JSON.parse(c.ConfigJson):c,x=T()({},s),Object.keys(x).forEach(function(b){b=="MyStudentColumn"?x[b].Checked=!1:o[b]&&(x[b].Checked=o[b].Checked)}),c.columns&&c.columns.forEach(function(b,se){var k;x["MyColumnData[".concat(b.Id,"]")]={Checked:((k=o.MyStudentColumn)===null||k===void 0?void 0:k.Checked)||!1,Name:b.ColumnName,props:{type:"input"}}}),R&&R(x)),S.next=12;break;case 7:S.prev=7,S.t0=S.catch(0),console.log("error"),console.log(S.t0),ae.error("\u521D\u59CB\u5316\u5217\u914D\u7F6E\u5931\u8D25");case 12:case"end":return S.stop()}},l,null,[[0,7]])}));return function(){return t.apply(this,arguments)}}();return(0,N.useEffect)(function(){v()},[]),(0,m.jsxs)(m.Fragment,{children:[te,(0,m.jsx)(V.Z,{onOk:function(){var t=_()(i()().mark(function l(a){return i()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:if(!f){c.next=3;break}return c.next=3,f.submit();case 3:case"end":return c.stop()}},l)}));return function(l){return t.apply(this,arguments)}}(),onClose:function(){P(!1)},onCancel:function(){P(!1)},title:"\u641C\u7D22\u9879\u76EE\u7684\u9009\u62E9",open:d,destroyOnClose:!0,width:650,className:"no-padding",footer:[(0,m.jsx)(A.ZP,{onClick:function(){P(!1)},children:"\u53D6\u6D88"},"select-items-modal-back"),(0,m.jsx)(A.ZP,{type:"primary",loading:E,onClick:_()(i()().mark(function t(){return i()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,f.submit();case 2:case"end":return a.stop()}},t)})),children:E?"\u6B63\u5728\u4FDD\u5B58":"\u4FDD\u5B58"},"select-items-modal-submit")],children:(0,m.jsx)("div",{className:"select-items",children:(0,m.jsx)(J.Z,{form:f,onFinish:function(l){var a=T()({},s);Object.keys(l).map(function(r){a[r].Checked=l[r]}),g(!0),console.log("items",JSON.stringify(a)),Object.keys(l).forEach(function(r){a[r]={Checked:l[r],Name:s[r].Name}}),console.log("items",JSON.stringify(a)),(0,W.ZP)("/SystemManage/UserSearch/setConfigJson",{method:"POST",data:{PageName:w,ConfigJson:JSON.stringify(a)}}).then(function(r){g(!1),r.success&&(P(!1),R&&R(a))})},layout:"inline",initialValues:Object.keys(s).reduce(function(t,l){return t[l]=!!s[l].Checked,t},{}),children:(0,m.jsx)(Z.Z,{children:Object.keys(s).map(function(t){return(0,m.jsx)(X.Z,{span:6,children:(0,m.jsx)("div",{style:{lineHeight:"60px"},children:(0,m.jsx)(J.Z.Item,{name:t,valuePropName:"checked",children:(0,m.jsx)(Y.Z,{children:s[t].Name},"select-items-form-".concat(t,"-Checkbox"))},"select-items-form-".concat(t))},"div-"+t)})})})},"select-items-form")})},"select-items-modal"),(0,m.jsx)(A.ZP,{onClick:function(){g(!1),P(!0)},children:"\u6761\u4EF6"},"columns-button")]})});$.Z=q},40402:function(he,$,e){e.r($),e.d($,{default:function(){return ne}});var G=e(15009),i=e.n(G),H=e(99289),T=e.n(H),Q=e(5574),_=e.n(Q),C=e(67294),L=e(45360),W=e(85576),J=e(42075),V=e(83622),A=e(11774),Z=e(49187),X=e(23750),Y={StudentName:{Name:"\u5B66\u5458\u59D3\u540D",Checked:!0},IdCard:{Name:"\u8EAB\u4EFD\u8BC1\u53F7",Checked:!0},Mobile:{Name:"\u624B\u673A\u53F7\u7801",Checked:!0},JxDeptId:{Name:"\u9A7E\u6821\u90E8\u95E8",Checked:!0},CostTypeId:{Name:"\u8D39\u7528\u7C7B\u578B",Checked:!0},PayTimes:{Name:"\u6302\u8D26\u65E5\u671F",Checked:!0},CreateTimes:{Name:"\u521B\u5EFA\u65E5\u671F",Checked:!0},Status:{Name:"\u72B6\u6001",Checked:!0}},N=function(d){var P,h,D,f,j,M,E,g,v=[];return(P=d.StudentName)!==null&&P!==void 0&&P.Checked&&v.push({type:"text",label:"\u5B66\u5458\u59D3\u540D",name:"StudentName",placeholder:"\u8BF7\u8F93\u5165\u5B66\u5458\u59D3\u540D"}),(h=d.IdCard)!==null&&h!==void 0&&h.Checked&&v.push({type:"text",label:"\u8EAB\u4EFD\u8BC1\u53F7",name:"IdCard",placeholder:"\u8BF7\u8F93\u5165\u8EAB\u4EFD\u8BC1\u53F7"}),(D=d.Mobile)!==null&&D!==void 0&&D.Checked&&v.push({type:"text",label:"\u624B\u673A\u53F7\u7801",name:"Mobile",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"}),(f=d.JxDeptId)!==null&&f!==void 0&&f.Checked&&v.push({type:"select",label:"\u9A7E\u6821\u90E8\u95E8",name:"JxDeptId",placeholder:"\u8BF7\u9009\u62E9\u9A7E\u6821\u90E8\u95E8",request:function(){var t=T()(i()().mark(function a(){var r;return i()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,(0,X.eB)();case 2:return r=o.sent,o.abrupt("return",r.success?r.data||[]:[]);case 4:case"end":return o.stop()}},a)}));function l(){return t.apply(this,arguments)}return l}()}),(j=d.CostTypeId)!==null&&j!==void 0&&j.Checked&&v.push({type:"select",label:"\u8D39\u7528\u7C7B\u578B",name:"CostTypeId",placeholder:"\u8BF7\u9009\u62E9\u8D39\u7528\u7C7B\u578B",request:function(){var t=T()(i()().mark(function a(){var r;return i()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,(0,Z.aB)();case 2:return r=o.sent,o.abrupt("return",r.success?r.data||[]:[]);case 4:case"end":return o.stop()}},a)}));function l(){return t.apply(this,arguments)}return l}()}),(M=d.PayTimes)!==null&&M!==void 0&&M.Checked&&v.push({type:"daterange",label:"\u6302\u8D26\u65E5\u671F",name:"PayTimes",placeholder:"\u8BF7\u9009\u62E9\u6302\u8D26\u65E5\u671F"}),(E=d.CreateTimes)!==null&&E!==void 0&&E.Checked&&v.push({type:"daterange",label:"\u521B\u5EFA\u65E5\u671F",name:"CreateTimes",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u65E5\u671F"}),(g=d.Status)!==null&&g!==void 0&&g.Checked&&v.push({type:"select",label:"\u72B6\u6001",name:"Status",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",options:[{label:"\u672A\u7F34\u8D39",value:"0"},{label:"\u90E8\u5206\u7F34\u8D39",value:"1"},{label:"\u5DF2\u7F34\u6E05",value:"2"}]}),v},K=e(78158),m=e(98820),q=e(51477),B=e(95641),le=e(54679),w=e(89545),s=e(85893),R=function(d){var P=d.open,h=d.onOpenChange,D=d.payFormInfo,f=d.onSuccess,j=d.messageApi,M=[{type:"hidden",label:"\u6302\u8D26ID",name:"ShouldPayId"},{type:"hidden",label:"\u5B66\u5458ID",name:"StudentId"},{type:"select",label:"\u4ED8\u6B3E\u65B9\u5F0F",name:"PayTypeId",required:!0,request:function(){var E=T()(i()().mark(function v(){var t;return i()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,Z.Q6)();case 2:return t=a.sent,a.abrupt("return",t.success?t.data||[]:[]);case 4:case"end":return a.stop()}},v)}));function g(){return E.apply(this,arguments)}return g}()},{type:"number",label:"\u7F34\u8D39\u91D1\u989D",name:"PayMoney",required:!0,min:.01},{type:"datetimepicker",label:"\u7F34\u8D39\u65F6\u95F4",name:"PayTime",required:!0},{type:"textarea",label:"\u7F34\u8D39\u5907\u6CE8",name:"Remark"}];return(0,s.jsx)(w.default,{insertTitle:"\u7F34\u8D39\u7A97\u53E3",open:P,onOpenChange:h,formItems:M,initData:D,setPath:"/Jx/Pay/JxPay/Pay",onCallBack:f,modifyTitle:"",getPath:""})},ee=R,z=e(63783),ae=e(66557),te=function(){var d=L.ZP.useMessage(),P=_()(d,2),h=P[0],D=P[1],f=(0,C.useRef)(null),j=(0,C.useRef)(null),M=(0,C.useRef)(null),E=(0,C.useState)([]),g=_()(E,2),v=g[0],t=g[1],l=(0,C.useState)(Y),a=_()(l,2),r=a[0],c=a[1],o=(0,C.useState)({}),x=_()(o,2),ue=x[0],S=x[1],b=(0,C.useState)([]),se=_()(b,2),k=se[0],fe=se[1],ve=(0,C.useState)("0"),oe=_()(ve,2),de=oe[0],ye=oe[1],pe=(0,C.useState)(!1),ie=_()(pe,2),Pe=ie[0],ce=ie[1],Ce=(0,C.useState)({}),me=_()(Ce,2),Se=me[0],Ie=me[1];(0,C.useEffect)(function(){var y=N(r);t(y)},[]);var _e=function(n){S(n)},ge=function(){var y=T()(i()().mark(function n(u){var I;return i()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.prev=0,p.next=3,(0,K.ZP)("/Jx/Pay/JxShouldPay/getShouldPaySummary",{method:"POST",data:u});case 3:I=p.sent,I.success&&ye(I.data.TotalMoney||"0"),p.next=10;break;case 7:p.prev=7,p.t0=p.catch(0),console.error("Failed to get payment summary:",p.t0);case 10:case"end":return p.stop()}},n,null,[[0,7]])}));return function(u){return y.apply(this,arguments)}}(),Me=function(n){h.loading({content:"\u83B7\u53D6\u4ED8\u6B3E\u4FE1\u606F",key:"loading",duration:0}),(0,K.ZP)("/Jx/Pay/JxShouldPay/getPayInfo",{method:"POST",data:{Id:n.Id}}).then(function(u){h.destroy("loading"),u&&u.success?(ce(!0),Ie(u.data)):h.error(u.message||"\u83B7\u53D6\u4ED8\u6B3E\u4FE1\u606F\u5931\u8D25")})},Ee=function(n){W.Z.confirm({title:"\u786E\u8BA4\u5220\u9664",icon:(0,s.jsx)(z.Z,{}),content:"\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u6761\u6302\u8D26\u8BB0\u5F55\u5417\uFF1F",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var u=T()(i()().mark(function F(){var p,re;return i()().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:return O.prev=0,O.next=3,(0,ae.Z)("/Jx/Pay/JxShouldPay/deleteJxShouldPay",{method:"DELETE",data:{Id:n}});case 3:p=O.sent,p.success?(h.success("\u5220\u9664\u6210\u529F"),(re=f.current)===null||re===void 0||re.reload()):h.error(p.message||"\u5220\u9664\u5931\u8D25"),O.next=10;break;case 7:O.prev=7,O.t0=O.catch(0),h.error("\u5220\u9664\u5931\u8D25");case 10:case"end":return O.stop()}},F,null,[[0,7]])}));function I(){return u.apply(this,arguments)}return I}()})},be=[{title:"\u5E8F\u53F7",dataIndex:"RowIndex",width:60,fixed:"left"},{title:"\u5B66\u5458\u59D3\u540D",dataIndex:"StudentName",width:100,fixed:"left",render:function(n,u){return(0,s.jsx)("a",{onClick:function(){var F;return(F=j.current)===null||F===void 0?void 0:F.show(u.StudentId)},children:n})}},{title:"\u8EAB\u4EFD\u8BC1\u53F7",dataIndex:"IdCard",width:180},{title:"\u624B\u673A\u53F7\u7801",dataIndex:"Mobile",width:120},{title:"\u9A7E\u6821\u90E8\u95E8",dataIndex:"JxDeptName",width:120},{title:"\u8D39\u7528\u7C7B\u578B",dataIndex:"CostTypeName",width:120},{title:"\u6302\u8D26\u91D1\u989D",dataIndex:"PayMoney",width:100,render:function(n){return(0,s.jsx)("span",{style:{color:"#f50"},children:n})}},{title:"\u6302\u8D26\u65E5\u671F",dataIndex:"PayTime",width:180},{title:"\u72B6\u6001",dataIndex:"StatusName",width:100,render:function(n,u){var I="";return u.Status==="0"?I="#f50":u.Status==="1"?I="#1890ff":u.Status==="2"&&(I="#52c41a"),(0,s.jsx)("span",{style:{color:I},children:n})}},{title:"\u521B\u5EFA\u4EBA",dataIndex:"CreateUserName",width:100},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"CreateTime",width:180},{title:"\u5907\u6CE8",dataIndex:"Remark",width:200,ellipsis:!0},{title:"\u64CD\u4F5C",dataIndex:"operation",fixed:"right",width:120,render:function(n,u){return(0,s.jsxs)(J.Z,{size:"small",children:[u.Status!=="2"&&(0,s.jsx)("a",{onClick:function(){return Me(u)},children:"\u7F34\u8D39"}),(0,s.jsx)("a",{onClick:function(){return Ee(u.Id)},children:"\u5220\u9664"})]})}}],Oe=[(0,s.jsx)(V.ZP,{type:"primary",onClick:function(){var n;(n=M.current)===null||n===void 0||n.show()},children:"\u65B0\u589E\u6302\u8D26"},"add-should-pay")];return(0,s.jsxs)(A._z,{header:{breadcrumb:{},title:""},children:[D,(0,s.jsx)(q.Z,{ref:f,formCols:v,setSearchParams:_e,onLoadCallback:function(n){return ge(n)},tableButtons:Oe,formButtons:[(0,s.jsx)(B.Z,{pageName:"JxShouldPayList",searchItems:r,onCallBack:function(n){c(n);var u=N(n);t(u)}},"ShouldPayList-PageSearchItems-1")],columns:be,rowKey:"Id",getPath:"/Jx/Pay/JxShouldPay/getShouldPayList",tableParams:ue,downloadPath:"/Jx/Pay/JxShouldPay/exportShouldPayList",downloadTableName:"JxShouldPayList",downloadSelectRowKeysName:"Ids",rowSelection:{selectedRowKeys:k,setSelectedRowKeys:fe,getCheckboxProps:function(n){return{disabled:!1}}},balance:de!=="0"?(0,s.jsxs)("span",{children:["\u6302\u8D26\u603B\u91D1\u989D\uFF1A",de,"\u5143"]}):null},"should-pay-list-table"),(0,s.jsx)(m.Z,{ref:j,StudentListRef:void 0,updateAddLoading:void 0}),(0,s.jsx)(le.Z,{ref:M,ShouldPayListRef:f,JxPayListRef:void 0}),(0,s.jsx)(ee,{open:Pe,onOpenChange:ce,payFormInfo:Se,onSuccess:function(){var n;(n=f.current)===null||n===void 0||n.reload()},messageApi:h})]})},ne=te}}]);
