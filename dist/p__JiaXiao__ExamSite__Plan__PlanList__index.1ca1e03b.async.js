"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1932],{54441:function(A,P){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M696 480H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"minus-circle",theme:"outlined"};P.Z=e},68639:function(A,P,e){var x=e(67294),i=e(54441),S=e(96750);function a(){return a=Object.assign?Object.assign.bind():function(u){for(var n=1;n<arguments.length;n++){var c=arguments[n];for(var m in c)Object.prototype.hasOwnProperty.call(c,m)&&(u[m]=c[m])}return u},a.apply(this,arguments)}const Z=(u,n)=>x.createElement(S.Z,a({},u,{ref:n,icon:i.Z})),d=x.forwardRef(Z);P.Z=d},51042:function(A,P,e){var x=e(67294),i=e(42110),S=e(96750);function a(){return a=Object.assign?Object.assign.bind():function(u){for(var n=1;n<arguments.length;n++){var c=arguments[n];for(var m in c)Object.prototype.hasOwnProperty.call(c,m)&&(u[m]=c[m])}return u},a.apply(this,arguments)}const Z=(u,n)=>x.createElement(S.Z,a({},u,{ref:n,icon:i.Z})),d=x.forwardRef(Z);P.Z=d},43425:function(A,P,e){var x=e(67294),i=e(34689),S=e(96750);function a(){return a=Object.assign?Object.assign.bind():function(u){for(var n=1;n<arguments.length;n++){var c=arguments[n];for(var m in c)Object.prototype.hasOwnProperty.call(c,m)&&(u[m]=c[m])}return u},a.apply(this,arguments)}const Z=(u,n)=>x.createElement(S.Z,a({},u,{ref:n,icon:i.Z})),d=x.forwardRef(Z);P.Z=d},4819:function(A,P,e){e.r(P),e.d(P,{default:function(){return he}});var x=e(15009),i=e.n(x),S=e(99289),a=e.n(S),Z=e(5574),d=e.n(Z),u=e(42509),n=e(11774),c=e(45360),m=e(83622),O=e(67294),te=e(51477),ne=e(13769),ae=e.n(ne),se=e(97857),C=e.n(se),H=e(23750),k=e(33725),V=e(64317),le=e(63783),re=e(68639),ie=e(51042),ue=e(43425),v=e(18922),Q=e(85576),oe=e(42075),K=e(25278),X=e(78158),t=e(85893),de=["key","name"],ce=function(N){var F=N.planId,Y=c.ZP.useMessage(),B=d()(Y,2),D=B[0],w=B[1],$=v.Z.useForm(),q=d()($,1),U=q[0],W=(0,O.useState)(!1),M=d()(W,2),_=M[0],b=M[1],J=(0,O.useState)(),T=d()(J,2),ee=T[0],z=T[1],G=function(){var y=a()(i()().mark(function E(){return i()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return D.loading({content:"\u6B63\u5728\u52A0\u8F7D\u6570\u636E",key:"loading"}),o.next=3,(0,X.ZP)("/Jx/ExamSite/Plan/getPlanDetailList",{method:"POST",data:{Id:F}}).then(function(p){D.destroy("loading"),p.success&&(z(p.data),b(!0))});case 3:case"end":return o.stop()}},E)}));return function(){return y.apply(this,arguments)}}(),L=function(E){var I=E.details.map(function(o){var p=o.StartTime,h=o.EndTime;try{p=o.StartTime.format("HH:mm:ss")}catch(j){}try{h=o.EndTime.format("HH:mm:ss")}catch(j){}return console.log(p),console.log(h),C()(C()({},o),{},{StartTime:p,EndTime:h})});console.log("\u63D0\u4EA4\u7684\u6570\u636E:",I),Q.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,t.jsx)(le.Z,{}),content:"\u662F\u5426\u786E\u8BA4\u4FDD\u5B58\u76F8\u5E94\u7684\u6570\u636E",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var o=a()(i()().mark(function h(){var j;return i()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,(0,X.ZP)("/Jx/ExamSite/Plan/setPlanDetailInfo",{method:"PUT",data:{data:I,PlanId:F}});case 2:j=l.sent,j&&j.success&&(b(!1),D.success(j.message));case 4:case"end":return l.stop()}},h)}));function p(){return o.apply(this,arguments)}return p}()})};return(0,t.jsxs)(t.Fragment,{children:[" ",w,(0,t.jsx)(Q.Z,{title:"\u8BA1\u5212\u660E\u7EC6\u8BBE\u7F6E\u7A97\u53E3",onOk:function(){U.submit()},onCancel:function(){b(!1)},open:_,destroyOnClose:!0,width:1200,className:"no-padding",children:(0,t.jsx)(v.Z,{form:U,name:"detailsForm",onFinish:L,className:"form-list",initialValues:{details:ee},children:(0,t.jsx)(v.Z.List,{name:"details",children:function(E,I){var o=I.add,p=I.remove;return(0,t.jsxs)(t.Fragment,{children:[E.map(function(h){var j=h.key,s=h.name,l=ae()(h,de);return(0,t.jsxs)(oe.Z,{style:{display:"flex",marginBottom:0,width:"100%"},align:"baseline",children:[(0,t.jsx)(v.Z.Item,C()(C()({},l),{},{name:[s,"Id"]})),(0,t.jsx)(v.Z.Item,{name:[s,"StartTime"],label:"\u573A\u6B21\u65F6\u95F4",children:(0,t.jsx)(k.Z,{placeholder:"\u5F00\u59CB\u65F6\u95F4",fieldProps:{format:"HH:mm:ss",showTime:{format:"HH:mm:ss"},picker:"time"}})}),(0,t.jsx)(v.Z.Item,{name:[s,"EndTime"],children:(0,t.jsx)(k.Z,{placeholder:"\u7ED3\u675F\u65F6\u95F4",fieldProps:{format:"HH:mm:ss",showTime:{format:"HH:mm:ss"},picker:"time"}})}),(0,t.jsx)(v.Z.Item,C()(C()({},l),{},{name:[s,"CarType"],style:{width:"80px"},children:(0,t.jsx)(V.Z,{request:a()(i()().mark(function f(){var g;return i()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,(0,H.kA)({ShowDetail:!1});case 2:if(g=r.sent,!g.success){r.next=7;break}return r.abrupt("return",g.data?g.data:[]);case 7:return r.abrupt("return",[]);case 8:case"end":return r.stop()}},f)})),placeholder:"\u51C6\u9A7E",fieldProps:{allowClear:!1,onSelect:function(g){}}})})),(0,t.jsx)(v.Z.Item,C()(C()({},l),{},{name:[s,"CarModelId"],style:{width:"200px",marginLeft:"20px"},children:(0,t.jsx)(V.Z,{request:a()(i()().mark(function f(){var g;return i()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,(0,H.G9)();case 2:if(g=r.sent,!g.success){r.next=7;break}return r.abrupt("return",g.data?g.data:[]);case 7:return r.abrupt("return",[]);case 8:case"end":return r.stop()}},f)})),width:200,placeholder:"\u8F66\u578B",fieldProps:{allowClear:!1,onSelect:function(g){}}})})),(0,t.jsx)(v.Z.Item,{name:[s,"MaxPeople"],label:"\u4EBA\u6570",style:{width:"150px"},children:(0,t.jsx)(K.Z,{placeholder:"\u6700\u5927\u4EBA\u6570"})}),(0,t.jsx)(v.Z.Item,{name:[s,"StartNumber"],label:"\u5E8F\u53F7",style:{width:"150px"},children:(0,t.jsx)(K.Z,{placeholder:"\u8D77\u59CB\u5E8F\u53F7"})}),(0,t.jsx)(v.Z.Item,{name:[s,"NickName"],label:"\u6635\u79F0",style:{width:"150px"},children:(0,t.jsx)(K.Z,{placeholder:"\u573A\u6B21\u6635\u79F0"})}),(0,t.jsx)(re.Z,{onClick:function(){return p(s)}})]},j)}),(0,t.jsx)(v.Z.Item,{children:(0,t.jsx)(m.ZP,{type:"dashed",onClick:function(){return o()},block:!0,icon:(0,t.jsx)(ie.Z,{}),children:"\u6DFB\u52A0\u573A\u6B21"})})]})}})})}),(0,t.jsx)("a",{onClick:function(){return G()},children:(0,t.jsx)(ue.Z,{})})]})},me=ce,fe=e(89545),ve=function(){var N=c.ZP.useMessage(),F=d()(N,2),Y=F[0],B=F[1],D=O.useRef(),w=O.useState(!1),$=d()(w,2),q=$[0],U=$[1],W=O.useState({}),M=d()(W,2),_=M[0],b=M[1],J=O.useState(!1),T=d()(J,2),ee=T[0],z=T[1],G=O.useState(""),L=d()(G,2),y=L[0],E=L[1],I=(0,O.useRef)(),o=function(l){return(0,t.jsx)("a",{onClick:function(){I.current.get(l.Id)},title:"\u7F16\u8F91",children:l.PlanName||"\u65E0"},"edit")},p=function(){var s=a()(i()().mark(function l(f){return i()().wrap(function(R){for(;;)switch(R.prev=R.next){case 0:E(f),z(!0);case 2:case"end":return R.stop()}},l)}));return function(f){return s.apply(this,arguments)}}(),h=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"NickName",title:"\u8BA1\u5212\u540D\u79F0",align:"center",render:function(l,f){return o(f)}},{title:"\u914D\u7F6E",render:function(l,f){return(0,t.jsx)(me,{planId:f.Id})},align:"center"},{dataIndex:"KeMuIdValue",title:"\u8BA1\u5212\u79D1\u76EE",align:"center"},{dataIndex:"FieldNickName",title:"\u6240\u5C5E\u8003\u573A",align:"center"},{dataIndex:"CreateUserName",title:"\u521B\u5EFA\u4EBA",align:"center"},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",align:"center"}],j=[(0,t.jsx)(m.ZP,{icon:(0,t.jsx)(u.Z,{}),ghost:!0,style:{marginLeft:"10px"},type:"primary",onClick:function(){return I.current.get(void 0)},children:"\u6DFB\u52A0\u8BA1\u5212"},"cost-type-add")];return(0,t.jsxs)(t.Fragment,{children:[B,(0,t.jsxs)(n._z,{header:{breadcrumb:{},title:""},children:[(0,t.jsx)(te.Z,{ref:D,tableButtons:j,formCols:[],columns:h,getPath:"/Jx/ExamSite/Plan/getPlanList",rowKey:"Id"}),(0,t.jsx)(fe.default,{ref:I,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1},{name:"PlanName",type:"input",label:"\u8BA1\u5212\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u540D\u79F0",required:!0},{type:"select",name:"FieldId",label:"\u8003\u8BD5\u573A\u5730",placeholder:"\u6E05\u9009\u62E9\u8003\u8BD5\u573A\u5730",request:H.OH,multiple:!1},{type:"select",name:"KeMuId",label:"\u8003\u8BD5\u79D1\u76EE",placeholder:"\u8BF7\u9009\u62E9\u8003\u8BD5\u79D1\u76EE",options:[{label:"\u79D1\u76EE\u4E00",value:1},{label:"\u79D1\u76EE\u4E8C",value:2},{label:"\u79D1\u76EE\u4E09",value:3},{label:"\u79D1\u76EE\u4E8C\u4E09",value:9},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:20},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:30}],multiple:!1,required:!0},{name:"OrderTip",type:"textarea",label:"\u9884\u7EA6\u63D0\u793A",placeholder:"\u8BF7\u8F93\u5165\u9884\u7EA6\u63D0\u793A",required:!1,height:80}],modifyTitle:"\u8BA1\u5212\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u8BA1\u5212",initData:{Id:void 0,SortCode:9999},getPath:"/Jx/ExamSite/Plan/getPlanInfo",setPath:"/Jx/ExamSite/Plan/setPlanInfo",width:500,onCallBack:function(){D.current.reload()}})]})]})},he=ve}}]);
