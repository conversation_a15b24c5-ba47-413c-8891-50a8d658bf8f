"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[942],{61569:function(Ae,G){var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};G.Z=a},85170:function(Ae,G){var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"};G.Z=a},57308:function(Ae,G,a){var me=a(67294),o=a(61569),Se=a(96750);function j(){return j=Object.assign?Object.assign.bind():function(le){for(var re=1;re<arguments.length;re++){var ue=arguments[re];for(var q in ue)Object.prototype.hasOwnProperty.call(ue,q)&&(le[q]=ue[q])}return le},j.apply(this,arguments)}const be=(le,re)=>me.createElement(Se.Z,j({},le,{ref:re,icon:o.Z})),C=me.forwardRef(be);G.Z=C},7625:function(Ae,G,a){a.r(G),a.d(G,{default:function(){return Ua}});var me=a(15009),o=a.n(me),Se=a(99289),j=a.n(Se),be=a(5574),C=a.n(be),le=a(51477),re=a(11774),ue=a(45360),q=a(85576),oe=a(83622),s=a(67294),ze=a(98820),Oe=a(89545),Ye=a(57308),e=a(85893),Xe=s.forwardRef(function(n,S){var b=ue.ZP.useMessage(),p=C()(b,2),I=p[0],w=p[1],$=(0,s.useRef)();return(0,e.jsxs)(e.Fragment,{children:[w,(0,e.jsx)(Oe.default,{ref:$,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1,value:n.userId},{name:"NewPWD",type:"password",label:"\u65B0\u7684\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801",required:!0},{name:"NewPWD2",type:"password",label:"\u786E\u8BA4\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u518D\u6B21\u5BC6\u7801",required:!0}],modifyTitle:"\u5BC6\u7801\u4FEE\u6539",insertTitle:"\u6DFB\u52A0\u8D44\u6599\u72B6\u6001",initData:{Id:n.userId},getPath:"",setPath:"/Auth/UserLogOn/changePassWord",width:400,onCallBack:function(){n.onCallBack&&n.onCallBack()}}),(0,e.jsx)("a",{onClick:function(){$.current.open()},children:(0,e.jsx)(Ye.Z,{})})]})}),Qe=Xe,Ge=a(6452),Te=a(66557),O=a(59637),qe=s.forwardRef(function(n,S){var b=function(I){I?Te.Z.post({message:"\u662F\u5426\u786E\u8BA4\u6253\u5F00 ".concat(n.realName," \u7684\u8D26\u53F7?"),setPath:"/SystemManage/User/openUser",data:{Id:n.userId},method:"PUT",messageApi:n.messageApi,onCallBack:function(){n.onCallBack&&n.onCallBack()}}):Te.Z.post({message:"\u662F\u5426\u786E\u8BA4\u5173\u95ED ".concat(n.realName," \u7684\u8D26\u53F7?"),setPath:"/SystemManage/User/closeUser",data:{Id:n.userId},method:"PUT",messageApi:n.messageApi,onCallBack:function(){n.onCallBack&&n.onCallBack()}})};return(0,e.jsx)(e.Fragment,{children:n.isEnabled?(0,e.jsx)(O.Z,{checked:!0,onChange:b}):(0,e.jsx)(O.Z,{checked:!1,onChange:b})})}),_e=qe,ea=a(19632),aa=a.n(ea),na=a(97857),ge=a.n(na),L=a(58930),De=a(2618),_=a(23750),we=a(88634),T=a(78158),ta=a(85170),ra=a(96750);function Be(){return Be=Object.assign?Object.assign.bind():function(n){for(var S=1;S<arguments.length;S++){var b=arguments[S];for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(n[p]=b[p])}return n},Be.apply(this,arguments)}const ua=(n,S)=>s.createElement(ra.Z,Be({},n,{ref:S,icon:ta.Z}));var la=s.forwardRef(ua),Ne=a(64121),r=a(18922),N=a(71230),We=a(15746),ce=a(42075),se=a(25278),ve=a(37720),sa=a(56595),$e=a(96074),ia=a(59720),da=a(60024),oa=a(80688),ca=a(27484),Ce=a.n(ca),fa=a(13769),ha=a.n(fa),ma=a(52677),ga=a.n(ma),va=a(68639),Ca=a(51042),pa=["key","name"],xa=s.forwardRef(function(n,S){var b=ue.ZP.useMessage(),p=C()(b,2),I=p[0],w=p[1],$=r.Z.useForm(),H=C()($,1),ee=H[0];(0,s.useEffect)(function(){ie()},[]),(0,s.useImperativeHandle)(S,function(){return{submit:function(){ee.submit()}}});var fe=function(B){var D=ae(B);(0,T.ZP)("/JiaXiao/JxClass/setClassCarTypePriceInfo",{method:"PUT",data:{UserId:n.userId,data:D.data}}).then(function(J){J.success&&I.success(J.message),n.onSubmitCallback&&n.onSubmitCallback()})},ie=function(){(0,T.ZP)("/JiaXiao/JxClass/getClassCarTypeByUserList",{method:"POST",data:{UserId:n.userId,size:9999}}).then(function(B){B.success&&ee.setFieldsValue({data:B.data.data}),n.onSubmitCallback&&n.onSubmitCallback()})};function ae(E){if(Array.isArray(E))return E.map(function(J){return ae(J)});var B={};for(var D in E)E[D]===null?B[D]=void 0:ga()(E[D])==="object"?B[D]=ae(E[D]):B[D]=E[D];return B}return(0,e.jsxs)("div",{className:"form-list",children:[w,(0,e.jsx)(r.Z,{form:ee,name:"form",onFinish:fe,layout:"vertical",onFinishFailed:function(){n.onSubmitCallback&&n.onSubmitCallback()},children:(0,e.jsx)(r.Z.List,{name:"data",children:function(B,D){var J=D.add,de=D.remove;return(0,e.jsxs)(e.Fragment,{children:[B.map(function(z){var K=z.key,W=z.name,V=ha()(z,pa);return(0,e.jsxs)(ce.Z,{style:{display:"flex",marginBottom:0,width:"100%"},align:"baseline",children:[(0,e.jsx)(r.Z.Item,{name:[W,"Id"],hidden:!0}),(0,e.jsx)(r.Z.Item,ge()(ge()({name:[W,"CarType"],label:K==0&&"\u8F66\u578B"},V),{},{rules:[{validator:function(){var U=j()(o()().mark(function d(M,P){return o()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(!(P==null||P=="")){f.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u8F66\u578B"),new Error;case 3:case"end":return f.stop()}},d)}));function R(d,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(L.Z,{title:"",width:200,listHeight:600,allowClear:!1,placeholder:"\u8F66\u578B",request:function(){return(0,_.kA)({SearchKey:"",ShowDetail:!1})},multiple:!1,onChange:function(R){}},"CarTypes-MySelect")})),(0,e.jsx)(r.Z.Item,{name:[W,"JxClassId"],label:K!=0?"":"\u62A5\u540D\u73ED\u578B",rules:[{validator:function(){var U=j()(o()().mark(function d(M,P){return o()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(!(P==null||P=="")){f.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u73ED\u578B"),new Error;case 3:case"end":return f.stop()}},d)}));function R(d,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(L.Z,{title:"",width:200,listHeight:600,allowClear:!1,placeholder:"\u62A5\u540D\u73ED\u578B",request:_.cL,multiple:!1,onChange:function(R){}},"JxClassId-MySelect")}),(0,e.jsx)(r.Z.Item,{name:[W,"PayMoney"],label:K!=0?"":"\u62A5\u540D\u4EF7\u94B1",rules:[{validator:function(){var U=j()(o()().mark(function d(M,P){return o()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(P!=""){f.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u62A5\u540D\u4EF7\u683C"),new Error;case 3:case"end":return f.stop()}},d)}));function R(d,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(se.Z,{placeholder:"\u62A5\u540D\u4EF7\u94B1",style:{width:140}})}),(0,e.jsx)(r.Z.Item,{name:[W,"FirstPayMoney"],label:K!=0?"":"\u9996\u4ED8",rules:[{validator:function(){var U=j()(o()().mark(function d(M,P){return o()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(P!=""){f.next=3;break}throw I.error("\u8BF7\u8F93\u5165\u9996\u4ED8\u91D1\u989D"),new Error;case 3:case"end":return f.stop()}},d)}));function R(d,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(se.Z,{placeholder:"\u9996\u4ED8",style:{width:140}})}),(0,e.jsx)(r.Z.Item,{name:[W,"Id"],label:K!=0?"":" ",children:(0,e.jsx)(va.Z,{onClick:function(){return de(W)}})})]},K)}),(0,e.jsx)(r.Z.Item,{style:{marginTop:10},children:(0,e.jsx)(oe.ZP,{type:"dashed",onClick:function(){return J()},block:!0,icon:(0,e.jsx)(Ca.Z,{}),children:"\u6DFB\u52A0\u914D\u7F6E"})})]})}})})]})}),ya=xa,Ia=s.forwardRef(function(n,S){return(0,s.useEffect)(function(){},[]),(0,e.jsx)(e.Fragment,{})}),Fa=Ia,ja=s.forwardRef(function(n,S){return(0,s.useEffect)(function(){},[]),(0,e.jsx)(e.Fragment,{})}),Za=ja,He=a(4584),Pa=a(96486),Ea=a(50335),Aa=a(84618),Sa=s.forwardRef(function(n,S){var b=(0,He.Z)(),p=C()(b,1),I=p[0],w=(0,He.Z)(),$=C()(w,1),H=$[0],ee=(0,s.useRef)(),fe=(0,s.useState)(!1),ie=C()(fe,2),ae=ie[0],E=ie[1],B=(0,s.useState)(!1),D=C()(B,2),J=D[0],de=D[1],z=(0,s.useState)(!1),K=C()(z,2),W=K[0],V=K[1],U=(0,s.useState)(void 0),R=C()(U,2),d=R[0],M=R[1],P=(0,s.useState)(void 0),ne=C()(P,2),f=ne[0],pe=ne[1],xe=(0,s.useState)("userinfo-1"),ye=C()(xe,2),Y=ye[0],Ue=ye[1],Ie=(0,s.useState)(1),Re=C()(Ie,2),te=Re[0],Pe=Re[1],ke=(0,s.useState)(),Fe=C()(ke,2),je=Fe[0],Ze=Fe[1];(0,s.useImperativeHandle)(S,function(){return{open:i}});var Le=(0,s.useState)(),Ee=C()(Le,2),he=Ee[0],x=Ee[1],i=function(){var g=j()(o()().mark(function v(){return o()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:de(!0),Ze(null),n.messageApi.loading({content:"\u6B63\u5728\u52A0\u8F7D",key:"loading",duration:0}),(0,T.WY)("/Tenant/getMyTenantType",{method:"POST"}).then(function(){var F=j()(o()().mark(function l(h){var A;return o()().wrap(function(Q){for(;;)switch(Q.prev=Q.next){case 0:if(!h.success){Q.next=7;break}return x(h.data),Q.next=4,(0,we.mC)();case 4:h=Q.sent,A=h.data,n.userId===void 0?(M({Id:void 0,AllowTime:[Ce()("2000-01-01"),Ce()("2099-01-01")],LockDate:[Ce()("2000-01-01"),Ce()("2000-01-01")],IsEnabled:!0,Sex:1,IsAdmin:!1}),E(!0),n.messageApi.destroy("loading")):(0,T.WY)("/SystemManage/UserInfo/".concat(n.userId),{method:"POST",data:{}}).then(function(u){u.success&&(M(u.data),A.length>0&&A.map(function(Ve){Ve.values=u.data.CategoryIds?u.data.CategoryIds.filter(function(Me){return Me.CategoryId==Ve.Id}).map(function(Me){return Me.Id}):[]}),pe(A),E(!0),n.messageApi.destroy("loading"))});case 7:case"end":return Q.stop()}},l)}));return function(l){return F.apply(this,arguments)}}());case 4:case"end":return t.stop()}},v)}));return function(){return g.apply(this,arguments)}}();(0,s.useEffect)(function(){d&&(I.setFieldsValue(d),Z(),de(!1))},[d]);var k={name:"File",showUploadList:!1,data:{Id:n.userId},beforeUpload:function(v){return v.type!=="image/png"&&v.type!=="image/jpeg"&&v.type!=="image/jpg"?(n.messageApi.error("\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301,\u7CFB\u7EDF\u53EA\u652F\u6301 png, jpeg, jpg \u7C7B\u578B\u7684\u56FE\u7247"),Ne.Z.LIST_IGNORE):(n.messageApi.loading({content:"\u6B63\u5728\u4E0A\u4F20",key:"loading",duration:0}),!0)},customRequest:function(){var g=j()(o()().mark(function c(t){var F,l,h,A,m;return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return F=t.file,l=t.onSuccess,h=t.onError,u.prev=1,A=new FormData,A.append("File",F),A.append("Id",n.userId||""),u.next=7,(0,T.WY)("/SystemManage/UserInfo/uploadHeadImg",{method:"formdata",data:A});case 7:m=u.sent,m.success?(n.messageApi.destroy("loading"),n.messageApi.success(m.message),l(m),i()):(n.messageApi.destroy("loading"),n.messageApi.error(m.message),h(m)),u.next=16;break;case 11:u.prev=11,u.t0=u.catch(1),n.messageApi.destroy("loading"),n.messageApi.error("\u4E0A\u4F20\u5931\u8D25"),h(u.t0);case 16:case"end":return u.stop()}},c,null,[[1,11]])}));function v(c){return g.apply(this,arguments)}return v}()},Z=function(){var v=(0,e.jsxs)(r.Z,{initialValues:d,form:I,onFinish:function(t){V(!0),t.Id=n.userId,t.Id==null&&(t.Id="00000000-0000-0000-0000-000000000000"),(0,T.WY)("/SystemManage/UserInfo/".concat(t.Id),{method:"PUT",data:t}).then(function(F){V(!1),F.success&&(n.messageApi.success(F.message),n.userId==null&&(I.resetFields(),E(!1),n.onCallBack&&n.onCallBack()))})},children:[(0,e.jsxs)(N.Z,{gutter:[32,8],children:[(0,e.jsxs)(We.Z,{span:19,children:[(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u771F\u5B9E\u59D3\u540D",name:"RealName",rules:[{validator:function(){var c=j()(o()().mark(function F(l,h){return o()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(h!=null){m.next=2;break}throw new Error("\u8BF7\u8F93\u5165\u7528\u6237\u59D3\u540D");case 2:case"end":return m.stop()}},F)}));function t(F,l){return c.apply(this,arguments)}return t}()}],children:(0,e.jsx)(se.Z,{name:"RealName",placeholder:"\u8BF7\u8F93\u5165\u771F\u5B9E\u59D3\u540D",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u624B\u673A\u53F7\u7801",name:"Phone",rules:[{validator:function(){var c=j()(o()().mark(function F(l,h){return o()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(h!=null){m.next=2;break}throw new Error("\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801");case 2:case"end":return m.stop()}},F)}));function t(F,l){return c.apply(this,arguments)}return t}()}],children:(0,e.jsx)(se.Z,{name:"Phone",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{label:"\u8D26\u6237\u72B6\u6001",name:"IsEnabled",style:{paddingLeft:"25px"},children:(0,e.jsx)(O.Z,{})})]}),(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u8BC1\u4EF6\u53F7\u7801",name:"IdCard",children:(0,e.jsx)(se.Z,{name:"IdCard",placeholder:"\u8BF7\u8F93\u5165\u8BC1\u4EF6\u53F7\u7801",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u6700\u9AD8\u6743\u9650",name:"IsAdmin",children:(0,e.jsx)(O.Z,{})}),(0,e.jsx)(r.Z.Item,{label:"\u7981\u6B62\u5FAE\u4FE1\u767B\u5F55",name:"DisableWxLogin",children:(0,e.jsx)(O.Z,{})}),(0,e.jsx)(r.Z.Item,{label:"\u7981\u6B62\u5FAE\u4FE1\u6D88\u606F",name:"DisableWxMessage",children:(0,e.jsx)(O.Z,{})})]}),(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u6240\u5C5E\u516C\u53F8",name:"CompanyName",children:(0,e.jsx)(se.Z,{placeholder:"\u8BF7\u8F93\u5165\u6240\u5C5E\u516C\u53F8",style:{width:"250px"}})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u7528\u6237\u6027\u522B",name:"Sex",children:(0,e.jsx)(L.Z,{allowClear:!1,options:[{label:"\u672A\u9009\u62E9",value:0},{label:"\u7537",value:1},{label:"\u5973",value:2}],multiple:!1,title:"",placeholder:"",onChange:function(t){}})}),(0,e.jsx)(r.Z.Item,{name:"EntryTime",label:"\u5165\u804C\u65F6\u95F4",children:(0,e.jsx)(Ea.Z,{style:{width:100},noStyle:!0})})]}),he=="ExamSite"&&(0,e.jsx)(ce.Z,{children:(0,e.jsx)(r.Z.Item,{name:"CommissionRate",label:"\u5206\u6DA6\u6BD4\u4F8B",children:(0,e.jsx)(ve.Z,{placeholder:"\u8BF7\u8F93\u5165\u5206\u6DA6\u6BD4\u4F8B",style:{width:"250px"},max:1,min:0})})})]}),n.userId!=null&&(0,e.jsxs)(We.Z,{style:{textAlign:"right"},span:5,children:[(0,e.jsx)(sa.Z,{preview:!1,width:110,height:128,id:"zp",src:d==null?void 0:d.HeadImgUrl,fallback:"https://cdn.51panda.com/no-image.png"}),(0,e.jsx)(Ne.Z,ge()(ge()({},k),{},{children:(0,e.jsx)(oe.ZP,{icon:(0,e.jsx)(la,{}),style:{marginTop:10},children:"\u4E0A\u4F20\u7167\u7247"})}))]})]}),he=="JiaXiao"&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u62A5\u540D\u95E8\u5E97",name:"JxDeptId",children:(0,e.jsx)(L.Z,{allowClear:!1,request:_.eB,listHeight:600,multiple:!1,title:"",placeholder:"",onChange:function(t){},width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"32px"},children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u573A\u5730",name:"JxFieldId",children:(0,e.jsx)(L.Z,{allowClear:!1,request:_.W9,listHeight:600,multiple:!1,title:"",placeholder:"",onChange:function(t){},width:300})})})]}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u79D1\u76EE",name:"KeMuIds",children:(0,e.jsx)(L.Z,{allowClear:!1,options:[{label:"\u79D1\u76EE\u4E8C",value:"2"},{label:"\u79D1\u76EE\u4E09",value:"3"},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:"20"},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:"30"}],maxTagCount:2,multiple:!0,title:"",placeholder:"",onChange:function(t){},width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"32px"},children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u8F66\u8F86",name:"CarIds",children:(0,e.jsx)(Aa.Z,{width:300,values:d==null?void 0:d.CarIds,onSelect:function(t){I.setFieldValue("CarIds",t)}})})})]}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u79D1\u4E8C\u9884\u7EA6",name:"RankClassId_2",children:(0,e.jsx)(L.Z,{allowClear:!1,multiple:!1,title:"",placeholder:"\u8BF7\u9009\u62E9\u79D1\u76EE\u4E8C\u9884\u7EA6\u7684\u9884\u7EA6\u6A21\u677F",onChange:function(t){},request:j()(o()().mark(function c(){var t;return o()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,(0,T.WY)("/JiaXiao/OrderCar/RankClass/getRankClassSelectList",{method:"POST",data:{KeMuIds:[2]}});case 2:if(t=l.sent,!t.success){l.next=7;break}return l.abrupt("return",t.data?t.data:[]);case 7:return l.abrupt("return",[]);case 8:case"end":return l.stop()}},c)})),width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"32px"},children:(0,e.jsx)(r.Z.Item,{label:"\u79D1\u4E09\u9884\u7EA6",name:"RankClassId_3",children:(0,e.jsx)(L.Z,{allowClear:!1,multiple:!1,title:"",placeholder:"\u8BF7\u9009\u62E9\u79D1\u76EE\u4E09\u9884\u7EA6\u7684\u9884\u7EA6\u6A21\u677F",onChange:function(t){},request:j()(o()().mark(function c(){var t;return o()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,(0,T.WY)("/JiaXiao/OrderCar/RankClass/getRankClassSelectList",{method:"POST",data:{KeMuIds:[3]}});case 2:if(t=l.sent,!t.success){l.next=7;break}return l.abrupt("return",t.data?t.data:[]);case 7:return l.abrupt("return",[]);case 8:case"end":return l.stop()}},c)})),width:300})})})]}),(0,e.jsx)($e.Z,{style:{},children:"\u64CD\u4F5C\u6743\u9650\u76F8\u5173"}),(0,e.jsx)(N.Z,{children:(0,e.jsx)(r.Z.Item,{label:"\u5173\u8054\u89D2\u8272",name:"RoleIds",children:(0,e.jsx)(L.Z,{allowClear:!1,request:De.KK,listHeight:600,multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:800})})}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u62A5\u540D\u5E97\u9762\u6743\u9650",name:"SearchJxDeptIds",children:(0,e.jsx)(L.Z,{allowClear:!1,request:_.eB,listHeight:600,multiple:!0,title:"",placeholder:"\u9009\u62E9\u53EF\u4EE5\u67E5\u770B\u7684\u5E97\u9762",onChange:function(t){},width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u57F9\u8BAD\u573A\u5730\u6743\u9650",name:"SearchJxFieldIds",children:(0,e.jsx)(L.Z,{allowClear:!1,request:_.W9,listHeight:600,multiple:!0,title:"",placeholder:"\u9009\u62E9\u53EF\u4EE5\u67E5\u770B\u7684\u8BAD\u7EC3\u573A",onChange:function(t){},width:300})})})]}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u53EF\u4EE5\u67E5\u8BE2\u6240\u6709\u7684\u62A5\u540D\u70B9\u548C\u8BAD\u7EC3\u573A\u7684\u5B66\u5458\u4FE1\u606F",name:"NoLockDeptAndField",children:(0,e.jsx)(O.Z,{})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EF\u4EE5\u5728\u6240\u6709\u7684\u62A5\u540D\u5E97\u9762\u6DFB\u52A0\u5B66\u5458\u4FE1\u606F",name:"NoLockAddDeptAndField",children:(0,e.jsx)(O.Z,{})})})]}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u63A8\u8350\u7684\u5B66\u5458",name:"LookOnlyMySaleStudent",children:(0,e.jsx)(O.Z,{})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u5F55\u5165\u7684\u5B66\u5458",name:"LookOnlyMyCreateStudent",children:(0,e.jsx)(O.Z,{})})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u63A8\u8350\u548C\u5F55\u5165\u7684\u5B66\u5458",name:"LookOnlyMySaleAndCreateStudent",children:(0,e.jsx)(O.Z,{})})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u62A5\u540D\u81EA\u52A8\u5206\u8F66",name:"AutoAssignCoach",children:(0,e.jsx)(O.Z,{})})})]}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u5929\u6570",name:"SearchDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u67E5\u8BE2\u7684\u5929\u6570",style:{width:"200px"}})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u8865\u5F55\u5929\u6570",name:"MakeUpDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u8865\u5F55\u7684\u5929\u6570",style:{width:"200px"}})})})]})]}),he=="ExamSite"&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)($e.Z,{style:{},children:"\u64CD\u4F5C\u6743\u9650\u76F8\u5173"}),(0,e.jsx)(N.Z,{children:(0,e.jsx)(r.Z.Item,{label:"\u5173\u8054\u89D2\u8272",name:"RoleIds",children:(0,e.jsx)(L.Z,{allowClear:!1,request:De.KK,listHeight:600,multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:800})})}),(0,e.jsxs)(N.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u5929\u6570",name:"SearchDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u67E5\u8BE2\u7684\u5929\u6570",style:{width:"200px"}})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u79D1\u76EE",name:"KeMuIds",children:(0,e.jsx)(L.Z,{allowClear:!1,options:[{label:"\u79D1\u76EE\u4E8C",value:2},{label:"\u79D1\u76EE\u4E09",value:3},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:20},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:30}],multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:500})})})]})]})]},"userinfo-1-form-"+te);Ze(v)},y=d?[{key:"userinfo-1",label:"\u57FA\u672C\u4FE1\u606F",children:je},{key:"userinfo-2",label:"\u5206\u7C7B\u7BA1\u7406",forceRender:!0,children:f!=null&&(0,e.jsxs)(e.Fragment,{children:[f!=null&&f.length==0&&(0,e.jsx)(ia.ZP,{status:"500",title:"500",subTitle:"\u7CFB\u7EDF\u6682\u672A\u914D\u7F6E\u76F8\u5E94\u7684\u5206\u7C7B\uFF0C\u5982\u9700\u8981\u524D\u5F80\u5206\u7C7B\u7BA1\u7406\u8FDB\u884C\u914D\u7F6E."}),f!=null&&f.map(function(g,v){return(0,e.jsxs)(r.Z,{initialValues:d,form:H,onFinish:function(t){V(!0);var F=Object.keys(t).filter(function(h){return h.startsWith("CategoryIds-")}).map(function(h){return t[h]}).filter(function(h){return Array.isArray(h)&&h.length>0}).reduce(function(h,A){return h.concat(A)},[]),l=aa()(new Set(F));(0,T.WY)("/SystemManage/Category/setUserCategoryInfo",{method:"PUT",data:{CategoryIds:l,Id:n.userId}}).then(function(h){V(!1),h.success&&n.messageApi.success(h.message)})},children:[(0,e.jsx)(r.Z.Item,{name:"CategoryIds-"+g.Id,hidden:!0,initialValue:g.values},"CategoryIds-"+g.Id+"-"+te),(0,e.jsx)(r.Z.Item,{label:g.Name,children:(0,e.jsx)(da.Z,{allowClear:!0,defaultValue:g.values,treeCheckable:!0,treeCheckStrictly:!0,treeDefaultExpandAll:!0,treeData:f[v].children,multiple:!0,title:"",placeholder:"\u8BF7\u786E\u8BA4\u9009\u62E9\u4E0A\u7EA7\u76EE\u5F55\uFF0C\u5982\u4E0D\u9009\u62E9\uFF0C\u5F53\u524D\u5206\u7C7B\u4F1A\u8BBE\u7F6E\u6210\u6839\u76EE\u5F55",maxTagCount:4,style:{width:"800px"},listHeight:600,fieldNames:{value:"Id",label:"Name",children:"children"},onChange:function(t){H.setFieldValue("CategoryIds-"+g.Id,t.map(function(F){return F.value}))}})})]},"userinfo-1-form-"+te)})]})}]:[],X=!d||n.userId==null?[]:[{key:"userinfo-3",label:"\u62A5\u540D\u4EF7\u683C",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(ya,{userId:n.userId,ref:ee,onSubmitCallback:function(){V(!1)}})})},{key:"userinfo-4",label:"\u64CD\u4F5C\u65E5\u5FD7",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(Fa,{userId:n.userId})})},{key:"userinfo-5",label:"\u4FEE\u6539\u65E5\u5FD7",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(Za,{userId:n.userId})})}];return(0,e.jsxs)(e.Fragment,{children:[ae&&(0,e.jsxs)(q.Z,{onOk:function(){Y=="userinfo-1"?I.submit():Y=="userinfo-2"?H.submit():Y=="userinfo-3"&&(V(!0),ee.current.submit())},open:ae,onCancel:function(){I.resetFields(),E(!1)},loading:J,confirmLoading:W,destroyOnClose:!0,title:d==null?"\u52A0\u8F7D\u4E2D...":n.userId!=null?d.RealName+" \u8D26\u6237\u7F16\u8F91":"\u521B\u5EFA\u65B0\u8D26\u6237",width:1200,children:[n.userId==null&&(0,e.jsx)(e.Fragment,{children:je}),n.userId!=null&&(0,e.jsx)(oa.Z,{activeKey:Y,onChange:function(v){Ue(v)},className:"no-padding-2",items:(d==null?void 0:d.TenantType)=="JiaXiao"?y.concat(X):y},"userinfo")]}),(0,e.jsx)("a",{onClick:function(){Pe(te+1),pe(void 0),M(void 0),i()},children:n.realName})]})}),Je=Sa,Ke=a(42509),ba=a(1291),Da=function(S){var b=S.open,p=S.onCancel,I=(0,s.useRef)();return(0,s.useEffect)(function(){b&&I.current&&I.current.open()},[b]),(0,e.jsx)(Oe.default,{ref:I,formItems:[{type:"tab",width:"100%",tabPosition:"top",defaultActiveKey:"audit",tabItems:[{key:"audit",label:"\u9700\u8981\u5BA1\u6838",children:[{type:"imagebase64",name:"",src:"/Auth/CreateUserCode/getAuditRegisterQCode",width:200,height:200,preview:!1,initPostData:void 0}]},{key:"direct",label:"\u76F4\u63A5\u6CE8\u518C",children:[{type:"imagebase64",name:"",src:"/Auth/CreateUserCode/getDirectRegisterQCode",width:200,height:200,preview:!1,initPostData:void 0}]}]}],modifyTitle:"\u626B\u7801\u6CE8\u518C",insertTitle:"\u626B\u7801\u6CE8\u518C",width:500,onCallBack:function(){},getPath:"",setPath:"",hideFooter:!0,onOpenChange:function($){$||p()}})},wa=Da,Ba=function(){var S=ue.ZP.useMessage(),b=C()(S,2),p=b[0],I=b[1],w=(0,s.useRef)(),$=(0,s.useRef)(null),H=(0,s.useRef)(null),ee=function(i){return(0,e.jsx)(Je,{userId:i.Id,realName:i.RealName,messageApi:p})},fe=function(i){return(0,e.jsx)(Qe,{userId:i.Id})},ie=function(i){return(0,e.jsx)(Ge.Z,{userId:i.Id,realName:i.RealName})},ae=function(i){return(0,e.jsx)(_e,{userId:i.Id,realName:i.RealName,isEnabled:i.IsEnabled,onCallBack:function(){w.current.reload()},messageApi:p})},E=s.useState([]),B=C()(E,2),D=B[0],J=B[1],de=s.useState([]),z=C()(de,2),K=z[0],W=z[1],V=(0,s.useState)(!1),U=C()(V,2),R=U[0],d=U[1],M=(0,s.useState)(!1),P=C()(M,2),ne=P[0],f=P[1],pe=(0,s.useState)([]),xe=C()(pe,2),ye=xe[0],Y=xe[1],Ue=(0,s.useState)([]),Ie=C()(Ue,2),Re=Ie[0],te=Ie[1],Pe=[{key:"enable",label:"\u6279\u91CF\u542F\u7528",onClick:function(i,k){var Z,y=0,X=i.length;Z=q.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",content:"\u786E\u8BA4\u8981\u542F\u7528\u9009\u4E2D\u7684 ".concat(i.length," \u4E2A\u7528\u6237\u5417\uFF1F"),okButtonProps:{loading:!1},onOk:function(){var g=j()(o()().mark(function c(){var t;return o()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return Z.update({title:"\u6B63\u5728\u6279\u91CF\u542F\u7528\u7528\u6237",content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(y,"/").concat(X),okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}}}),t=function(){var h=j()(o()().mark(function A(m){return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!(m>=i.length)){u.next=7;break}return Z.destroy(),p.success("\u6279\u91CF\u542F\u7528\u5B8C\u6210\uFF01"),Y([]),te([]),w.current.reload(),u.abrupt("return");case 7:return u.prev=7,u.next=10,(0,T.WY)("/SystemManage/User/openUser",{method:"PUT",data:{id:i[m]}});case 10:return y++,Z.update({content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(y,"/").concat(X)}),u.next=14,t(m+1);case 14:u.next=20;break;case 16:u.prev=16,u.t0=u.catch(7),Z.destroy(),p.error("\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF");case 20:case"end":return u.stop()}},A,null,[[7,16]])}));return function(m){return h.apply(this,arguments)}}(),l.next=4,t(0);case 4:case"end":return l.stop()}},c)}));function v(){return g.apply(this,arguments)}return v}()})}},{key:"disable",label:"\u6279\u91CF\u7981\u7528",onClick:function(i,k){var Z,y=0,X=i.length;Z=q.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",content:"\u786E\u8BA4\u8981\u7981\u7528\u9009\u4E2D\u7684 ".concat(i.length," \u4E2A\u7528\u6237\u5417\uFF1F"),okButtonProps:{loading:!1},onOk:function(){var g=j()(o()().mark(function c(){var t;return o()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return Z.update({title:"\u6B63\u5728\u6279\u91CF\u7981\u7528\u7528\u6237",content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(y,"/").concat(X),okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}}}),t=function(){var h=j()(o()().mark(function A(m){return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!(m>=i.length)){u.next=7;break}return Z.destroy(),p.success("\u6279\u91CF\u7981\u7528\u5B8C\u6210\uFF01"),Y([]),te([]),w.current.reload(),u.abrupt("return");case 7:return u.prev=7,u.next=10,(0,T.WY)("/SystemManage/User/closeUser",{method:"PUT",data:{id:i[m]}});case 10:return y++,Z.update({content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(y,"/").concat(X)}),u.next=14,t(m+1);case 14:u.next=20;break;case 16:u.prev=16,u.t0=u.catch(7),Z.destroy(),p.error("\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF");case 20:case"end":return u.stop()}},A,null,[[7,16]])}));return function(m){return h.apply(this,arguments)}}(),l.next=4,t(0);case 4:case"end":return l.stop()}},c)}));function v(){return g.apply(this,arguments)}return v}()})}}],ke={selectedRowKeys:ye,setSelectedRowKeys:Y,onChange:function(i,k){Y(i),te(k)}};(0,s.useEffect)(function(){var x=function(){var i=j()(o()().mark(function k(){return o()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:(0,T.WY)("/SystemManage/Category/getCategoryList",{method:"POST"}).then(function(X){var g=[];X.data.map(function(v){g.push({dataIndex:"Column-".concat(v.Id),title:v.Name,align:"center",search:!1})}),console.log("_columns:"+g),W(g),J([{title:"\u5E8F\u53F7",dataIndex:"RowIndex",align:"center"},{title:"\u7F16\u53F7",dataIndex:"SysId",align:"center"},{title:"\u8D26\u53F7",dataIndex:"Account",align:"center"},{title:"\u540D\u5B57",dataIndex:"RealName",render:function(c,t){return ee(t)},align:"center"},{title:"\u7535\u8BDD",dataIndex:"Phone",align:"center"},{title:"\u8F66\u724C",dataIndex:"CarNumbers",align:"center"},{title:"\u5BC6\u7801",render:function(c,t){return fe(t)},align:"center"},{title:"\u5FAE\u4FE1",render:function(c,t){return ie(t)},align:"center"},{title:"\u72B6\u6001",render:function(c,t){return ae(t)},align:"center"},{title:"\u516C\u53F8\u540D\u79F0",dataIndex:"CompanyName",align:"center"},{title:"\u57F9\u8BAD\u573A\u5730",dataIndex:"JxDeptName",align:"center"},{title:"\u8BAD\u7EC3\u573A\u5730",dataIndex:"JxFieldName",align:"center"}].concat(g,[{title:"\u521B\u5EFA\u4EBA",dataIndex:"CreateUserName",align:"center"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"CreateTime",align:"center"}]))});case 1:case"end":return y.stop()}},k)}));return function(){return i.apply(this,arguments)}}();x()},[]);var Fe=[{name:"SearchKey",type:"input",label:"\u5173\u952E\u5B57\u8BCD",placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57\u3001\u8D26\u53F7\u3001\u7535\u8BDD\u7B49",allowClear:!0},{name:"IsEnableds",type:"select",label:"\u7528\u6237\u72B6\u6001",placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u72B6\u6001",options:[{label:"\u6709\u6548",value:"1"},{label:"\u65E0\u6548",value:"0"}],value:["1"],multiple:!0,allowClear:!0},{name:"IsAdmins",type:"select",label:"\u6700\u9AD8\u6743\u9650",placeholder:"\u9009\u62E9\u662F\u5426\u6709\u6700\u9AD8\u6743\u9650\u7684",options:[{label:"\u662F",value:"1"},{label:"\u5426",value:"0"}],allowClear:!0,onChange:function(){}},{name:"RoleId",type:"select",label:"\u7528\u6237\u89D2\u8272",placeholder:"\u9009\u62E9\u7528\u6237\u89D2\u8272\u6765\u67E5\u8BE2",request:De.KK,allowClear:!0,onChange:function(){}},{name:"JxDeptIds",type:"select",label:"\u6240\u5C5E\u95E8\u5E97",placeholder:"\u9009\u62E9\u7528\u6237\u6240\u5C5E\u95E8\u5E97",request:_.eB,multiple:!0,allowClear:!0,onChange:function(){}},{name:"JxFieldIds",type:"select",label:"\u6240\u5C5E\u573A\u5730",placeholder:"\u9009\u62E9\u7528\u6237\u6240\u5C5E\u573A\u5730",request:_.W9,allowClear:!0,onChange:function(){}},{name:"CategoryId",type:"selecttree",label:"\u7528\u6237\u5206\u7C7B",placeholder:"\u9009\u62E9\u7528\u6237\u5206\u7C7B",request:we.mC,maxTagCount:1,allowClear:!0,onChange:function(i){w.current.setFormData("CategoryId",i)}},{name:"CategoryParentId",type:"selecttree",label:"\u5206\u7C7B\u4E0A\u7EA7",placeholder:"\u9009\u62E9\u7528\u6237\u5206\u7C7B\u4E0A\u7EA7",request:we.mC,maxTagCount:1,allowClear:!0,onChange:function(i){w.current.setFormData("CategoryParentId",i)}}],je=function(){d(!1),f(!0)},Ze=function(){f(!1)},Le=function(){d(!0)},Ee=function(){d(!1)},he=[(0,e.jsx)(oe.ZP,{icon:(0,e.jsx)(Ke.Z,{}),type:"primary",onClick:Le,ghost:!0,children:"\u6DFB\u52A0"},"add-button"),(0,e.jsx)(q.Z,{title:"\u9009\u62E9\u6DFB\u52A0\u65B9\u5F0F",open:R,onCancel:Ee,footer:null,centered:!0,children:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-around",padding:"20px"},children:[(0,e.jsx)(oe.ZP,{type:"primary",icon:(0,e.jsx)(Ke.Z,{}),onClick:function(){H.current.open(),d(!1)},size:"large",style:{height:"45px",width:"140px",fontSize:"16px"},children:"\u76F4\u63A5\u6DFB\u52A0"}),(0,e.jsx)(oe.ZP,{type:"primary",icon:(0,e.jsx)(ba.Z,{}),onClick:je,size:"large",style:{height:"45px",width:"140px",fontSize:"16px"},children:"\u626B\u7801\u6CE8\u518C"})]})},"add-modal")];return(0,e.jsxs)(re._z,{header:{breadcrumb:{},title:""},children:[I,D.length>0&&(0,e.jsx)(le.Z,{ref:w,formCols:Fe,columns:D,tableButtons:he,getPath:"/SystemManage/User/getUserList",downloadPath:"/SystemManage/User/exportUserList",downloadTableName:"UserList",rowKey:"Id",rowSelection:ke,batchOperations:Pe,transformedData:function(i){return i.map(function(k){if(k.CategoryDatas&&Array.isArray(k.CategoryDatas)){var Z={};k.CategoryDatas.forEach(function(y){Z[y.CategoryId]||(Z[y.CategoryId]=[]),Z[y.CategoryId].push(y.CategoryName)}),Object.keys(Z).forEach(function(y){k["Column-".concat(y)]=Z[y].join(", ")})}}),i}},"my-list-table"),(0,e.jsx)(ze.Z,{ref:$,StudentListRef:void 0,updateAddLoading:void 0}),(0,e.jsx)(Je,{userId:void 0,realName:void 0,ref:H,messageApi:p}),(0,e.jsx)(wa,{open:ne,onCancel:Ze})]})},Ua=Ba}}]);
