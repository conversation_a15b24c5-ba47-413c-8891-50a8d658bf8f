"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5911],{66212:function(U,x,e){var O=e(67294),f=e(99011),T=e(96750);function h(){return h=Object.assign?Object.assign.bind():function(c){for(var g=1;g<arguments.length;g++){var y=arguments[g];for(var p in y)Object.prototype.hasOwnProperty.call(y,p)&&(c[p]=y[p])}return c},h.apply(this,arguments)}const E=(c,g)=>O.createElement(T.Z,h({},c,{ref:g,icon:f.Z})),m=O.forwardRef(E);x.Z=m},92849:function(U,x,e){e.r(x),e.d(x,{default:function(){return ne}});var O=e(15009),f=e.n(O),T=e(99289),h=e.n(T),E=e(5574),m=e.n(E),c=e(67294),g=e(7825),y=e(4393),p=e(18922),Y=e(45360),K=e(21532),R=e(71230),Z=e(15746),G=e(34041),J=e(83498),X=e(42075),N=e(83622),Q=e(11774),k=e(66212),w=e(29177),b=e(27484),P=e.n(b),q=e(23750),$=e(78158),a=e(85893),_=g.Z.Title,ee=function(C){var u=C.plan,v=C.onToggle,B=C.onRefresh,F=(0,c.useRef)(),j=function(){var D;(D=F.current)===null||D===void 0||D.open({PlanDetailId:u.PlanDetailId,PeopleCount:u.PeopleCount||0,ControlDate:u.ControlDate})};return(0,a.jsx)(y.Z,{size:"small",className:"plan-card",title:(0,a.jsx)("div",{className:"card-title",children:(0,a.jsxs)("div",{className:"time",children:[(0,a.jsx)(k.Z,{style:{marginRight:"5px"}})," ",P()(u.StartTime,"HH:mm:ss").format("HH:mm")]})}),children:(0,a.jsxs)("div",{className:"card-content",children:[(0,a.jsx)("div",{className:"status-badge ".concat(u.IsOpen?"active":"inactive"),onClick:function(){return v(u.PlanDetailId,u.ControlDate,!u.IsOpen)},children:u.IsOpen?"\u5DF2\u5F00\u542F":"\u5DF2\u5173\u95ED"}),(0,a.jsx)("div",{className:"capacity",children:(0,a.jsxs)("span",{children:[(0,a.jsx)(w.Z,{style:{marginRight:"5px"}}),"\u5DF2\u7EA6 ",u.PeopleCount||0]})})]})})},ae=function(){var C=p.Z.useForm(),u=m()(C,1),v=u[0],B=Y.ZP.useMessage(),F=m()(B,2),j=F[0],S=F[1],D=(0,c.useState)(!1),H=m()(D,2),te=H[0],L=H[1],se=(0,c.useState)([]),V=m()(se,2),A=V[0],M=V[1],le=(0,c.useState)([]),W=m()(le,2),ie=W[0],oe=W[1];(0,c.useEffect)(function(){de()},[]);var de=function(){var o=h()(f()().mark(function t(){var n,s;return f()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,q.OH)();case 3:n=r.sent,n!=null&&n.success&&Array.isArray(n.data)&&(oe(n.data),n.data.length>0&&(s=n.data[0].value,v.setFieldValue("fieldId",s),I(s))),r.next=11;break;case 7:r.prev=7,r.t0=r.catch(0),j.error("\u83B7\u53D6\u8003\u573A\u5217\u8868\u5931\u8D25"),console.error("Failed to fetch fields:",r.t0);case 11:case"end":return r.stop()}},t,null,[[0,7]])}));return function(){return o.apply(this,arguments)}}(),I=function(){var o=h()(f()().mark(function t(n){var s,l;return f()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return L(!0),i.prev=1,s=v.getFieldValue("controlDate")||P()(),i.next=5,(0,$.ZP)("/Jx/ExamSite/PlanDetailOpen/getOpenList",{method:"POST",data:{FieldId:n,ControlDate:s.format("YYYY-MM-DD")}});case 5:l=i.sent,l!=null&&l.success&&Array.isArray(l.data)?M(l.data):(j.warning("\u672A\u83B7\u53D6\u5230\u6392\u73ED\u6570\u636E"),M([])),i.next=14;break;case 9:i.prev=9,i.t0=i.catch(1),j.error("\u83B7\u53D6\u6392\u73ED\u6570\u636E\u5931\u8D25"),console.error("Failed to fetch plan details:",i.t0),M([]);case 14:return i.prev=14,L(!1),i.finish(14);case 17:case"end":return i.stop()}},t,null,[[1,9,14,17]])}));return function(n){return o.apply(this,arguments)}}(),ue=function(){var o=h()(f()().mark(function t(n,s,l){var r;return f()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.prev=0,d.next=3,(0,$.ZP)("/Jx/ExamSite/PlanDetailOpen/togglePlanDetail",{method:"POST",data:{PlanDetailId:n,ControlDate:s,IsOpen:l}});case 3:r=d.sent,r!=null&&r.success?(j.success(l?"\u5F00\u542F\u6210\u529F":"\u5173\u95ED\u6210\u529F"),I(v.getFieldValue("fieldId"))):j.error((r==null?void 0:r.message)||"\u64CD\u4F5C\u5931\u8D25"),d.next=11;break;case 7:d.prev=7,d.t0=d.catch(0),j.error("\u64CD\u4F5C\u5931\u8D25"),console.error("Toggle failed:",d.t0);case 11:case"end":return d.stop()}},t,null,[[0,7]])}));return function(n,s,l){return o.apply(this,arguments)}}(),ce=c.useMemo(function(){if(!Array.isArray(A))return{};var o=A.reduce(function(t,n){var s="".concat(n.CarModelName,"-").concat(n.CarType);return t[s]||(t[s]=[]),t[s].push(n),t},{});return Object.keys(o).forEach(function(t){o[t].sort(function(n,s){return P()(n.StartTime,"HH:mm:ss").diff(P()(s.StartTime,"HH:mm:ss"))})}),o},[A]);return(0,a.jsxs)(Q._z,{header:{breadcrumb:{},title:""},children:[S,(0,a.jsx)(K.ZP,{theme:{components:{Form:{itemMarginBottom:0}}},children:(0,a.jsx)(y.Z,{className:"no-padding",children:(0,a.jsx)(p.Z,{form:v,name:"advanced-search",style:{maxWidth:"none",padding:"20px 12px 8px 20px"},onFinish:function(){var o=h()(f()().mark(function t(n){return f()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,I(n.fieldId);case 2:case"end":return l.stop()}},t)}));return function(t){return o.apply(this,arguments)}}(),children:(0,a.jsxs)(R.Z,{gutter:24,children:[(0,a.jsx)(Z.Z,{span:8,style:{marginBottom:"12px"},children:(0,a.jsx)(p.Z.Item,{name:"fieldId",label:"\u6240\u5C5E\u8003\u573A",style:{width:"100%"},children:(0,a.jsx)(G.default,{options:ie,placeholder:"\u8BF7\u9009\u62E9\u8003\u573A"})})}),(0,a.jsx)(Z.Z,{span:8,style:{marginBottom:"12px"},children:(0,a.jsx)(p.Z.Item,{name:"controlDate",label:"\u8003\u8BD5\u65E5\u671F",initialValue:P()(),style:{width:"100%"},children:(0,a.jsx)(J.default,{style:{width:"100%"}})})}),(0,a.jsx)(Z.Z,{span:8,style:{textAlign:"right",float:"right",marginBottom:"12px"},children:(0,a.jsxs)(X.Z,{children:[(0,a.jsx)(N.ZP,{onClick:function(){v.resetFields(),v.submit()},children:"\u91CD\u7F6E"}),(0,a.jsx)(N.ZP,{type:"primary",htmlType:"submit",loading:te,children:"\u67E5\u8BE2"})]})})]})})})}),(0,a.jsx)("div",{className:"plan-content",children:Object.entries(ce).map(function(o){var t=m()(o,2),n=t[0],s=t[1],l=n.split("-"),r=m()(l,2),i=r[0],d=r[1];return(0,a.jsx)(y.Z,{title:(0,a.jsxs)(_,{level:4,className:"group-title",children:[i," (",d,")"]}),className:"group-card",bordered:!1,style:{marginTop:"12px"},children:(0,a.jsx)(R.Z,{gutter:[16,16],children:s.map(function(z){return(0,a.jsx)(Z.Z,{xs:24,sm:12,md:8,lg:6,xl:4,style:{minWidth:"250px"},children:(0,a.jsx)(ee,{plan:z,onToggle:ue,onRefresh:function(){return I(v.getFieldValue("fieldId"))}})},z.PlanDetailId)})})},n)})})]})},ne=ae}}]);
