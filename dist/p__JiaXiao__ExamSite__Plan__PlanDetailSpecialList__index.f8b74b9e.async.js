"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4750],{66212:function(W,O,a){var I=a(67294),f=a(99011),M=a(96750);function p(){return p=Object.assign?Object.assign.bind():function(m){for(var y=1;y<arguments.length;y++){var x=arguments[y];for(var h in x)Object.prototype.hasOwnProperty.call(x,h)&&(m[h]=x[h])}return m},p.apply(this,arguments)}const S=(m,y)=>I.createElement(M.Z,p({},m,{ref:y,icon:f.Z})),v=I.forwardRef(S);O.Z=v},61929:function(W,O,a){a.r(O),a.d(O,{default:function(){return te}});var I=a(15009),f=a.n(I),M=a(99289),p=a.n(M),S=a(5574),v=a.n(S),m=a(67294),y=a(7825),x=a(4393),h=a(18922),z=a(45360),U=a(21532),B=a(71230),j=a(15746),J=a(34041),G=a(83498),b=a(42075),N=a(83622),X=a(11774),Q=a(64317),k=a(66212),w=a(29177),q=a(27484),P=a.n(q),_=a(23750),$=a(78158),ee=a(89545),e=a(85893),ae=y.Z.Title,ne=function(D){var u=D.plan,o=D.onToggle,A=D.onRefresh,E=(0,m.useRef)(),g=function(){var C;(C=E.current)===null||C===void 0||C.open({PlanDetailId:u.PlanDetailId,MaxPeople:u.MaxPeople||0,OrderDate:u.OrderDate})};return(0,e.jsxs)(x.Z,{size:"small",className:"plan-card",title:(0,e.jsxs)("div",{className:"card-title",children:[(0,e.jsxs)("div",{className:"time",children:[(0,e.jsx)(k.Z,{style:{marginRight:"5px"}})," ",P()(u.StartTime,"HH:mm:ss").format("HH:mm")]}),(0,e.jsxs)("div",{className:"available-capacity",onClick:g,style:{cursor:"pointer",color:(u.MaxPeople||0)<0?"#f5222d":"inherit"},children:["\u53EF\u7EA6 ",u.MaxPeople||0," \u4EBA"]})]}),children:[(0,e.jsxs)("div",{className:"card-content",children:[(0,e.jsx)("div",{className:"status-badge ".concat(u.IsOpen?"active":"inactive"),onClick:function(){return o(u.PlanDetailId,u.OrderDate,!u.IsOpen)},children:u.IsOpen?"\u5DF2\u5F00\u542F":"\u5DF2\u5173\u95ED"}),(0,e.jsx)("div",{className:"capacity",children:(0,e.jsxs)("span",{children:[(0,e.jsx)(w.Z,{style:{marginRight:"5px"}}),"\u5DF2\u7EA6 ",u.PeopleCount||0]})})]}),(0,e.jsx)(ee.default,{ref:E,insertTitle:"\u4FEE\u6539\u53EF\u7EA6\u4EBA\u6570",modifyTitle:"\u4FEE\u6539\u53EF\u7EA6\u4EBA\u6570",width:400,getPath:"",setPath:"/Jx/ExamSite/PlanDetailSpecial/setMaxPeople",formItems:[{label:"\u53EF\u7EA6\u4EBA\u6570",name:"MaxPeople",type:"number",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u53EF\u7EA6\u4EBA\u6570"}]},{label:"PlanDetailId",name:"PlanDetailId",type:"hidden"},{label:"OrderDate",name:"OrderDate",type:"hidden"}],onCallBack:function(){A()}})]})},le=function(){var D=h.Z.useForm(),u=v()(D,1),o=u[0],A=z.ZP.useMessage(),E=v()(A,2),g=E[0],Z=E[1],C=(0,m.useState)(!1),H=v()(C,2),se=H[0],V=H[1],ie=(0,m.useState)([]),Y=v()(ie,2),T=Y[0],R=Y[1],de=(0,m.useState)([]),K=v()(de,2),ue=K[0],oe=K[1];(0,m.useEffect)(function(){ce()},[]);var ce=function(){var i=p()(f()().mark(function t(){var n,r;return f()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.prev=0,l.next=3,(0,_.OH)();case 3:n=l.sent,n!=null&&n.success&&Array.isArray(n.data)&&(oe(n.data),n.data.length>0&&(r=n.data[0].value,o.setFieldValue("fieldId",r),F(r))),l.next=11;break;case 7:l.prev=7,l.t0=l.catch(0),g.error("\u83B7\u53D6\u8003\u573A\u5217\u8868\u5931\u8D25"),console.error("Failed to fetch fields:",l.t0);case 11:case"end":return l.stop()}},t,null,[[0,7]])}));return function(){return i.apply(this,arguments)}}(),F=function(){var i=p()(f()().mark(function t(n){var r,s;return f()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return V(!0),d.prev=1,r=o.getFieldValue("controlDate")||P()(),d.next=5,(0,$.ZP)("/Jx/ExamSite/PlanDetailSpecial/getPlanDetailSpecialList",{method:"POST",data:{KeMuId:o.getFieldValue("KeMuId"),FieldId:n,OrderDates:[r.format("YYYY-MM-DD"),r.format("YYYY-MM-DD")]}});case 5:s=d.sent,s!=null&&s.success&&Array.isArray(s.data)?R(s.data):(g.warning("\u672A\u83B7\u53D6\u5230\u6392\u73ED\u6570\u636E"),R([])),d.next=14;break;case 9:d.prev=9,d.t0=d.catch(1),g.error("\u83B7\u53D6\u6392\u73ED\u6570\u636E\u5931\u8D25"),console.error("Failed to fetch plan details:",d.t0),R([]);case 14:return d.prev=14,V(!1),d.finish(14);case 17:case"end":return d.stop()}},t,null,[[1,9,14,17]])}));return function(n){return i.apply(this,arguments)}}(),me=function(){var i=p()(f()().mark(function t(n,r,s){var l;return f()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.prev=0,c.next=3,(0,$.ZP)("/Jx/ExamSite/PlanDetailSpecial/toggleStatus",{method:"POST",data:{PlanDetailId:n,OrderDate:r,IsOpen:s}});case 3:l=c.sent,l!=null&&l.success?(g.success(s?"\u5F00\u542F\u6210\u529F":"\u5173\u95ED\u6210\u529F"),F(o.getFieldValue("fieldId"))):g.error((l==null?void 0:l.message)||"\u64CD\u4F5C\u5931\u8D25"),c.next=11;break;case 7:c.prev=7,c.t0=c.catch(0),g.error("\u64CD\u4F5C\u5931\u8D25"),console.error("Toggle failed:",c.t0);case 11:case"end":return c.stop()}},t,null,[[0,7]])}));return function(n,r,s){return i.apply(this,arguments)}}(),fe=m.useMemo(function(){if(!Array.isArray(T))return{};var i=T.reduce(function(t,n){var r="".concat(n.CarModelName,"-").concat(n.CarType);return t[r]||(t[r]=[]),t[r].push(n),t},{});return Object.keys(i).forEach(function(t){i[t].sort(function(n,r){return P()(n.StartTime,"HH:mm:ss").diff(P()(r.StartTime,"HH:mm:ss"))})}),i},[T]),ve=function(){o.resetFields(),o.setFieldValue("controlDate",P()()),o.submit()};return(0,e.jsxs)(X._z,{header:{breadcrumb:{},title:""},children:[Z,(0,e.jsx)(U.ZP,{theme:{components:{Form:{itemMarginBottom:0}}},children:(0,e.jsx)(x.Z,{className:"no-padding",children:(0,e.jsx)(h.Z,{form:o,name:"advanced-search",style:{maxWidth:"none",padding:"20px 12px 8px 20px"},onFinish:function(){var i=p()(f()().mark(function t(n){return f()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=2,F(n.fieldId);case 2:case"end":return s.stop()}},t)}));return function(t){return i.apply(this,arguments)}}(),children:(0,e.jsxs)(B.Z,{gutter:24,children:[(0,e.jsx)(j.Z,{span:8,style:{marginBottom:"12px"},children:(0,e.jsx)(h.Z.Item,{name:"fieldId",label:"\u6240\u5C5E\u8003\u573A",style:{width:"100%"},children:(0,e.jsx)(J.default,{options:ue,placeholder:"\u8BF7\u9009\u62E9\u8003\u573A"})})}),(0,e.jsx)(j.Z,{span:8,style:{marginBottom:"12px"},children:(0,e.jsx)(h.Z.Item,{name:"KeMuId",label:"\u9884\u7EA6\u79D1\u76EE",style:{width:"100%"},children:(0,e.jsx)(Q.Z,{options:[{label:"\u79D1\u76EE\u4E8C",value:2},{label:"\u79D1\u76EE\u4E09",value:3},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:20},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:30}],placeholder:"\u8BF7\u9009\u62E9\u9884\u7EA6\u79D1\u76EE"})})}),(0,e.jsx)(j.Z,{span:8,style:{marginBottom:"12px"},children:(0,e.jsx)(h.Z.Item,{name:"controlDate",label:"\u8003\u8BD5\u65E5\u671F",initialValue:P()(),style:{width:"100%"},children:(0,e.jsx)(G.default,{style:{width:"100%"},onChange:function(t){var n=o.getFieldValue("fieldId");n&&F(n)}})})}),(0,e.jsx)(j.Z,{span:8}),(0,e.jsx)(j.Z,{span:8}),(0,e.jsx)(j.Z,{span:8,style:{textAlign:"right",float:"right",marginBottom:"12px"},children:(0,e.jsxs)(b.Z,{children:[(0,e.jsx)(N.ZP,{onClick:function(){o.resetFields(),o.submit()},children:"\u91CD\u7F6E"}),(0,e.jsx)(N.ZP,{type:"primary",htmlType:"submit",loading:se,children:"\u67E5\u8BE2"})]})})]})})})}),(0,e.jsx)("div",{className:"plan-content",children:Object.entries(fe).map(function(i){var t=v()(i,2),n=t[0],r=t[1],s=n.split("-"),l=v()(s,2),d=l[0],c=l[1];return(0,e.jsx)(x.Z,{title:(0,e.jsxs)(ae,{level:4,className:"group-title",children:[d," (",c,")"]}),className:"group-card",bordered:!1,style:{marginTop:"12px"},children:(0,e.jsx)(B.Z,{gutter:[16,16],children:r.map(function(L){return(0,e.jsx)(j.Z,{xs:24,sm:12,md:8,lg:6,xl:4,style:{minWidth:"250px"},children:(0,e.jsx)(ne,{plan:L,onToggle:me,onRefresh:function(){return F(o.getFieldValue("fieldId"))}})},L.PlanDetailId)})})},n)})})]})},te=le}}]);
