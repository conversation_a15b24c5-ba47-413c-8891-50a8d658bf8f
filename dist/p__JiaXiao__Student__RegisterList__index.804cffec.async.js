"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[979],{91767:function(Ke,he,s){s.r(he),s.d(he,{default:function(){return Ue}});var ye=s(15009),o=s.n(ye),Oe=s(99289),v=s.n(Oe),Pe=s(97857),je=s.n(Pe),Fe=s(5574),d=s.n(Fe),f=s(78158),X=s(63783),ge=s(42509),Te=s(60433),i=s(67294),Ze=s(49495),$e=s(96750);function b(){return b=Object.assign?Object.assign.bind():function(P){for(var I=1;I<arguments.length;I++){var C=arguments[I];for(var h in C)Object.prototype.hasOwnProperty.call(C,h)&&(P[h]=C[h])}return P},b.apply(this,arguments)}const De=(P,I)=>i.createElement($e.Z,b({},P,{ref:I,icon:Ze.Z}));var Be=i.forwardRef(De),Se=s(11774),me=s(17014),q=s(37476),ve=s(5966),Le=s(90672),Ie=s(45360),xe=s(83622),z=s(85576),ke=s(98820),Re=s(66309),ze=s(25278),Je=s(21532),Ae=s(57513),t=s(85893),Ne=i.forwardRef(function(P,I){var C=Ie.ZP.useMessage(),h=d()(C,2),x=h[0],J=h[1],_=i.useState(!1),A=d()(_,2),ee=A[0],te=A[1],j=(0,i.useState)(""),N=d()(j,2),y=N[0],E=N[1];(0,i.useImperativeHandle)(I,function(){return{open:function(e){E(e),$(!0),r({},1,O)}}});var G=i.useState(!1),ne=d()(G,2),ae=ne[0],$=ne[1],pe=i.useState(""),re=d()(pe,2),Ce=re[0],M=re[1],we=(0,i.useState)([]),se=d()(we,2),ue=se[0],F=se[1],ie=(0,i.useState)(0),D=d()(ie,2),de=D[0],U=D[1],le=(0,i.useState)(!1),B=d()(le,2),oe=B[0],K=B[1],T=(0,i.useState)(1),L=d()(T,2),k=L[0],H=L[1],Q=(0,i.useState)(15),ce=d()(Q,2),O=ce[0],Y=ce[1],fe=i.useState(Object),V=d()(fe,2),Z=V[0],W=V[1],l=function(e,n){return"\u7B2C ".concat(n[0],"-").concat(n[1]," \u6761/\u603B\u5171 ").concat(e," \u6761")},r=function(e,n,m){W(e),K(!0),e.current=n,e.pageSize=m,H(n),Y(m),(0,f.ZP)("/Jx/Student/Student/getStudentList",{method:"POST",data:e}).then(function(w){var c=[],g=w.data.data;g.map(function(R){c.push(R.Id)}),(0,f.ZP)("/Jx/Student/StudentRegister/GetStudentRegisterList",{method:"POST",data:{StudentIds:c,RegisterId:y==""?void 0:y}}).then(function(R){g.forEach(function(S){R.data.includes(S.Id)?S.InDetail=!0:S.InDetail=!1}),K(!1),U(w.data.total),F(g)})})},a=[{title:"\u6DFB\u52A0",fixed:"left",align:"center",width:50,render:function(e,n){return n.InDetail?(0,t.jsx)("a",{style:{color:"gray"},children:"\u6DFB\u52A0"}):(0,t.jsx)("a",{onClick:function(){return z.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,t.jsx)(X.Z,{}),content:"\u662F\u5426\u5C06\u8BE5\u5B66\u5458\u6DFB\u52A0\u5165\u540D\u518C?",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var w=v()(o()().mark(function g(){return o()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:x.loading({content:"\u6B63\u5728\u64CD\u4F5C\u6570\u636E",key:"loading",duration:0}),(0,f.ZP)("/Jx/Student/StudentRegister/addStudentToRegister",{method:"POST",data:{StudentId:n.Id,RegisterId:y}}).then(function(p){x.destroy("loading"),p!=null&&p.success&&(x.success(p.message),r(Z,k,O))});case 2:case"end":return S.stop()}},g)}));function c(){return w.apply(this,arguments)}return c}()})},children:"\u6DFB\u52A0"})}},{title:"\u79FB\u9664",fixed:"left",align:"center",width:50,render:function(e,n){return n.InDetail?(0,t.jsx)("a",{onClick:function(){return z.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,t.jsx)(X.Z,{}),content:"\u662F\u5426\u5C06\u8BE5\u5B66\u5458\u79FB\u9664\u540D\u518C?",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var w=v()(o()().mark(function g(){return o()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:x.loading({content:"\u6B63\u5728\u64CD\u4F5C\u6570\u636E",key:"loading",duration:0}),(0,f.ZP)("/Jx/Student/StudentRegister/removeStudentFromRegister",{method:"POST",data:{StudentId:n.Id,RegisterId:y}}).then(function(p){x.destroy("loading"),p!=null&&p.success&&(x.success(p.message),r(Z,k,O))});case 2:case"end":return S.stop()}},g)}));function c(){return w.apply(this,arguments)}return c}()})},children:"\u79FB\u9664"}):(0,t.jsx)("a",{style:{color:"gray"},children:"\u79FB\u9664"})}},{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"xm",title:"\u540D\u5B57",align:"center",render:function(e,n){return(0,t.jsx)("a",{children:n.NoPay>0?(0,t.jsx)(Re.Z,{color:"red",children:n.xm}):(0,t.jsx)(Re.Z,{children:n.xm})})}},{dataIndex:"sfzmhm",title:"\u8BC1\u4EF6\u53F7\u7801",align:"center"},{dataIndex:"SaleUserName",title:"\u4ECB\u7ECD\u4EBA",align:"center"},{dataIndex:"CarType",title:"\u8F66\u578B",align:"center"},{dataIndex:"JxClassName",title:"\u73ED\u578B",align:"center"},{dataIndex:"JxDeptName",title:"\u57F9\u8BAD\u573A\u5730",align:"center"},{dataIndex:"JxFieldName",title:"\u8BAD\u7EC3\u573A\u5730",align:"center"}];return(0,t.jsxs)(t.Fragment,{children:[J,(0,t.jsx)(z.Z,{onOk:function(e){e.Id===""&&(e.Id=void 0),(0,f.ZP)("/Jx/Student/StudentRegister/setRegisterInfo",{method:"PUT",data:e}).then(function(n){n&&n.success&&(te(!1),x.success(n.message))})},title:"\u5B66\u5458\u641C\u7D22\u6DFB\u52A0",open:ae,onCancel:function(){$(!1)},footer:!1,width:1e3,className:"no-padding no-padding-1",children:(0,t.jsxs)(Se._z,{header:{breadcrumb:{},title:""},children:[(0,t.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:10},children:(0,t.jsx)("div",{style:{width:"45%"},children:(0,t.jsx)(ze.Z.Search,{placeholder:"\u8F93\u5165\u5173\u952E\u5B57\u641C\u7D22",style:{width:"60%"},onSearch:function(e){M(e),r({SearchKey:e},1,O)},enterButton:!0})})}),(0,t.jsx)(Je.ZP,{theme:{token:{fontSize:12.5}},children:(0,t.jsx)(Ae.Z,{scroll:{x:"max-content"},size:"small",bordered:!1,dataSource:ue,loading:oe,pagination:{total:de,onChange:function(e,n){r(Z,e,n)},showTotal:l,pageSize:O,current:k,pageSizeOptions:[15,30,100,200,500]},columns:a},"user-list-table")},"student-list-table-config")]})})]})}),Ee=Ne,Ge=s(80320),Me=function(){var I=Ie.ZP.useMessage(),C=d()(I,2),h=C[0],x=C[1],J=i.useRef(),_=i.useRef(),A=i.useState(!1),ee=d()(A,2),te=ee[0],j=ee[1],N=i.useState(Object),y=d()(N,2),E=y[0],G=y[1],ne=i.useRef(),ae=i.useState(!1),$=d()(ae,2),pe=$[0],re=$[1],Ce=i.useState(""),M=d()(Ce,2),we=M[0],se=M[1],ue=i.useRef(),F=i.useRef(),ie=i.useState(!1),D=d()(ie,2),de=D[0],U=D[1],le=i.useState(""),B=d()(le,2),oe=B[0],K=B[1],T=(0,i.useRef)(null),L=(0,i.useRef)(null),k=i.useState(""),H=d()(k,2),Q=H[0],ce=H[1],O=i.useState(!1),Y=d()(O,2),fe=Y[0],V=Y[1],Z=function(r){r===void 0?(G({Id:void 0}),j(!0)):(0,f.ZP)("/Jx/Student/StudentRegister/getRegisterInfo",{method:"POST",data:{Id:r}}).then(function(a){a&&a.success&&(G(je()({},a.data)),j(!0))})},W=function(r){return new Promise(function(a){r.Id===""&&(r.Id=void 0),(0,f.ZP)("/Jx/Student/StudentRegister/setRegisterInfo",{method:"PUT",data:r}).then(function(u){if(u&&u.success){var e;j(!1),h.success(u.message),(e=J.current)===null||e===void 0||e.reload(),a(!0)}else a(!1)})})};return(0,t.jsxs)(t.Fragment,{children:[x,(0,t.jsxs)(Se._z,{header:{breadcrumb:{},title:""},children:[(0,t.jsx)(q.Y,{title:"\u6CE8\u518C\u540D\u5355\u6587\u6863",open:fe,onOpenChange:V,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},submitter:{render:function(r,a){return[(0,t.jsx)(xe.ZP,{type:"primary",onClick:function(){window.open(Q)},children:"\u65B0\u7A97\u53E3\u6253\u5F00"},"newOpen")]}},width:800,children:(0,t.jsx)("iframe",{style:{border:0,width:"100%",height:"70vh"},src:Q,id:"pdfFrame"})}),(0,t.jsxs)(q.Y,{onFinish:function(){var l=v()(o()().mark(function r(a){return o()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,W(a);case 2:case"end":return e.stop()}},r)}));return function(r){return l.apply(this,arguments)}}(),title:E.Id!=null?"\u540D\u518C\u7F16\u8F91":"\u6DFB\u52A0\u540D\u518C",formRef:_,open:te,onOpenChange:j,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},initialValues:E,width:500,style:{paddingTop:"10px"},children:[(0,t.jsx)(ve.Z,{readonly:!0,hidden:!0,name:"Id"}),(0,t.jsx)(ve.Z,{name:"Name",label:"",placeholder:"\u8BF7\u8F93\u5165\u540D\u518C\u540D\u79F0",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u518C\u540D\u79F0!"}]}),(0,t.jsx)(Le.Z,{name:"Remark",label:"",placeholder:"\u8BF7\u8F93\u5165\u7C7B\u578B\u5907\u6CE8"})]}),(0,t.jsx)(q.Y,{onFinish:function(){var l=v()(o()().mark(function r(a){return o()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,W(a);case 2:case"end":return e.stop()}},r)}));return function(r){return l.apply(this,arguments)}}(),title:"\u540D\u5355\u660E\u7EC6",formRef:ue,open:de,onOpenChange:U,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},submitter:!1,width:900,children:(0,t.jsx)(me.Z,{cardBordered:!0,actionRef:F,className:"no-padding",columns:[{title:"\u79FB\u9664",fixed:"left",align:"center",width:50,render:function(r,a){return[(0,t.jsx)("a",{onClick:function(){z.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,t.jsx)(X.Z,{}),content:"\u662F\u5426\u5C06\u8BE5\u5B66\u5458\u6DFB\u79FB\u9664\u540D\u518C?",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var e=v()(o()().mark(function m(){return o()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.next=2,(0,f.ZP)("/Jx/Student/StudentRegister/removeRegisterDetail",{method:"POST",data:{Id:a.Id}}).then(function(g){if(g&&g.success){var R;h.success(g.message),F==null||(R=F.current)===null||R===void 0||R.reload()}});case 2:case"end":return c.stop()}},m)}));function n(){return e.apply(this,arguments)}return n}(),onCancel:function(){var e=v()(o()().mark(function m(){return o()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:case"end":return c.stop()}},m)}));function n(){return e.apply(this,arguments)}return n}()})},children:"\u79FB\u9664"},"removeDetail")]},hideInSearch:!0},{valueType:"indexBorder",dataIndex:"Index",title:"\u5E8F\u53F7",fixed:"left",align:"center",width:50,search:!1},{dataIndex:"xm",title:"\u59D3\u540D",ellipsis:!0,align:"center",width:80,hideInSearch:!0,render:function(r,a){return[(0,t.jsx)("a",{onClick:function(){var e;T==null||(e=T.current)===null||e===void 0||e.GetStudentInfo(a.Id)},title:"\u7F16\u8F91",children:a.xm},"edit")]}},{dataIndex:"sfzmhm",title:"\u8BC1\u4EF6\u53F7\u7801",ellipsis:!0,align:"center",width:140,hideInSearch:!0},{dataIndex:"CarType",title:"\u8F66\u578B",ellipsis:!0,align:"center",width:50,hideInSearch:!0},{dataIndex:"SaleUserName",title:"\u63A8\u8350\u4EBA",ellipsis:!0,align:"center",width:100,hideInSearch:!0},{dataIndex:"CreateTime",title:"\u62A5\u540D\u65E5\u671F",ellipsis:!0,align:"center",width:140,hideInSearch:!0},{dataIndex:"xzjcx",title:"\u73B0\u8F66\u578B",ellipsis:!0,align:"center",width:80,hideInSearch:!0}],request:v()(o()().mark(function l(){var r,a,u=arguments;return o()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return r=u.length>0&&u[0]!==void 0?u[0]:{},r.RegisterId=oe,n.next=4,(0,f.ZP)("/Jx/Student/StudentRegister/getRegisterDetailList",{method:"POST",data:r});case 4:return a=n.sent,n.abrupt("return",{success:a.success,data:a.data.data,total:a.data.total});case 6:case"end":return n.stop()}},l)})),rowKey:"Id",size:"small",toolBarRender:!1,search:!1,options:!1,pagination:{showSizeChanger:!0,showQuickJumper:!0,defaultPageSize:10}},"register-detail-list")}),(0,t.jsx)(me.Z,{cardBordered:!0,scroll:{x:"max-content"},actionRef:J,columns:[{valueType:"indexBorder",dataIndex:"Index",title:"\u5E8F\u53F7",fixed:"left",align:"center",hideInSearch:!1},{dataIndex:"Name",title:"\u540D\u518C\u540D\u79F0",align:"center",ellipsis:!0,render:function(r,a){return[(0,t.jsx)("a",{onClick:function(){Z(a.Id)},title:"\u7F16\u8F91",children:a.Name},"edit")]}},{title:"\u6DFB\u52A0",render:function(r,a){return[(0,t.jsx)("a",{onClick:function(){var e;(e=L.current)===null||e===void 0||e.open(a.Id)},title:"\u6DFB\u52A0",children:(0,t.jsx)(ge.Z,{})},"add")]},align:"center",hideInSearch:!0},{title:"\u660E\u7EC6",render:function(r,a){return[(0,t.jsx)("a",{onClick:function(){K(a.Id),U(!0)},title:"\u660E\u7EC6",children:(0,t.jsx)(Te.Z,{})},"detail")]},align:"center",hideInSearch:!0},{title:"\u4E0B\u8F7D",render:function(r,a){return[(0,t.jsx)("a",{onClick:v()(o()().mark(function u(){return o()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return h.loading({content:"\u6B63\u5728\u4E0B\u8F7D\u6587\u6863",key:"loading",duration:0}),n.next=3,(0,f.ZP)("/Jx/Student/StudentRegister/exportRegisterDetailList",{method:"POST",responseType:"blob",data:{RegisterId:a.Id}}).then(function(m){Ge.Z.downLoadFile(m),h.destroy("loading")});case 3:case"end":return n.stop()}},u)})),title:"\u6253\u5370",children:(0,t.jsx)(Be,{})},"print")]},align:"center",hideInSearch:!0},{dataIndex:"StudentCount",title:"\u540D\u518C\u4EBA\u6570",align:"center",hideInSearch:!0},{dataIndex:"CreateUserName",title:"\u521B\u5EFA\u4EBA",hideInSearch:!0},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",hideInSearch:!0}],request:v()(o()().mark(function l(){var r,a,u=arguments;return o()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return r=u.length>0&&u[0]!==void 0?u[0]:{},n.next=3,(0,f.ZP)("/Jx/Student/StudentRegister/GetRegisterList",{method:"POST",data:r});case 3:return a=n.sent,n.abrupt("return",{success:a.success,data:a.data.data,total:a.data.total});case 5:case"end":return n.stop()}},l)})),rowKey:"Id",pagination:{showSizeChanger:!0,showQuickJumper:!0,defaultPageSize:10},headerTitle:"\u540D\u518C\u7BA1\u7406",toolBarRender:function(){return[(0,t.jsx)(xe.ZP,{icon:(0,t.jsx)(ge.Z,{}),type:"primary",onClick:function(){Z(void 0)},children:"\u6DFB\u52A0\u540D\u518C"},"add")]}},"Id"),(0,t.jsx)(ke.Z,{ref:T,StudentListRef:void 0,updateAddLoading:void 0}),(0,t.jsx)(Ee,{ref:L})]})]})},Ue=Me}}]);
