"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9323],{2710:function(w,v){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M956.9 845.1L896.4 632V168c0-17.7-14.3-32-32-32h-704c-17.7 0-32 14.3-32 32v464L67.9 845.1C60.4 866 75.8 888 98 888h828.8c22.2 0 37.6-22 30.1-42.9zM200.4 208h624v395h-624V208zm228.3 608l8.1-37h150.3l8.1 37H428.7zm224 0l-19.1-86.7c-.8-3.7-4.1-6.3-7.8-6.3H398.2c-3.8 0-7 2.6-7.8 6.3L371.3 816H151l42.3-149h638.2l42.3 149H652.7z"}}]},name:"laptop",theme:"outlined"};v.Z=e},31936:function(w,v,e){var B=e(67294),f=e(2710),j=e(96750);function x(){return x=Object.assign?Object.assign.bind():function(g){for(var I=1;I<arguments.length;I++){var b=arguments[I];for(var O in b)Object.prototype.hasOwnProperty.call(b,O)&&(g[O]=b[O])}return g},x.apply(this,arguments)}const C=(g,I)=>B.createElement(j.Z,x({},g,{ref:I,icon:f.Z})),i=B.forwardRef(C);v.Z=i},7135:function(w,v,e){e.r(v),e.d(v,{default:function(){return Y}});var B=e(5574),f=e.n(B),j=e(51477),x=e(11774),C=e(45360),i=e(67294),g=e(89545),I=e(49187),b=e(42509),O=e(83622),t=e(85893),A=i.forwardRef(function(a,M){var m=C.ZP.useMessage(),u=f()(m,2),L=u[0],o=u[1],l=(0,i.useRef)(),y=[{label:"\u5F00\u653E",value:"true"},{label:"\u5173\u95ED",value:"false"}],r=[{label:"\u662F",value:"true"},{label:"\u5426",value:"false"}];return(0,t.jsxs)(t.Fragment,{children:[o,(0,t.jsx)(g.default,{ref:l,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1},{type:"group",children:[{name:"Name",type:"input",label:"\u573A\u5730\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u573A\u5730\u540D\u79F0",width:200,required:!0},{name:"Address",type:"input",label:"\u573A\u5730\u5730\u5740",placeholder:"\u8BF7\u8F93\u5165\u573A\u5730\u5730\u5740",width:484,required:!1}]},{type:"group",children:[{name:"AccountId",type:"select",request:I.Gs,multiple:!1,label:"\u6536\u6B3E\u8D26\u6237",placeholder:"\u8BF7\u9009\u62E9\u6536\u6B3E\u8D26\u6237",width:200,required:!0},{name:"Phone",type:"input",label:"\u8054\u7CFB\u7535\u8BDD",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD",width:200,required:!0},{name:"SortCode",type:"input",label:"\u663E\u793A\u6392\u5E8F",placeholder:"\u8BF7\u8F93\u5165\u663E\u793A\u6392\u5E8F",width:200,required:!0}]},{type:"divider",label:"\u6536\u8D39\u914D\u7F6E",style:{marginTop:"0",marginBottom:"0"}},{type:"group",children:[{type:"number",width:200,name:"FreeMinutes",label:"\u514D\u8D39\u65F6\u95F4",placeholder:"\u514D\u8D39\u65F6\u95F4\uFF08\u5206\u949F\uFF09",required:!0},{type:"number",width:200,name:"MaxPriceOneDay",label:"\u6BCF\u65E5\u5C01\u9876",placeholder:"\u6BCF\u65E5\u5C01\u9876\uFF08\u5143\uFF09",required:!0},{type:"number",width:200,name:"MaxCarCount",label:"\u8F66\u4F4D\u4E0A\u9650",placeholder:"\u8BF7\u8F93\u5165\u8F66\u4F4D\u4E0A\u9650\uFF0C\u8F93\u51650 \u5219\u4E0D\u9650\u5236",required:!0}]},{type:"group",children:[{type:"number",width:200,name:"UnitOfTime",label:"\u8BA1\u8D39\u5468\u671F",placeholder:"\u8BA1\u8D39\u5468\u671F\uFF08\u5206\u949F\uFF09",required:!0},{type:"number",width:200,name:"UnitOfTimePrice",label:"\u5468\u671F\u6536\u8D39",placeholder:"\u8BF7\u8F93\u5165\u5468\u671F\u6536\u8D39",required:!0}]},{type:"group",children:[{type:"timerange",width:200,name:"NightTimes",label:"\u591C\u95F4\u6536\u8D39",placeholder:"\u9009\u62E9\u591C\u95F4\u6536\u8D39\u65F6\u95F4"},{type:"number",width:200,name:"NightUnitOfTime",label:"\u591C\u95F4\u8BA1\u8D39",placeholder:"\u8BF7\u8F93\u591C\u95F4\u8BA1\u8D39\u5468\u671F\uFF08\u5206\u949F\uFF09",required:!0},{type:"number",width:200,name:"NightUnitOfTimePrice",label:"\u5468\u671F\u6536\u8D39",placeholder:"\u8BF7\u8F93\u5165\u591C\u95F4\u5468\u671F\u6536\u8D39",required:!0}]},{type:"divider",label:"\u5F00\u653E\u914D\u7F6E",style:{marginTop:"0",marginBottom:"0"}},{type:"group",children:[{type:"switch",name:"IsOpen",label:"\u5F00\u653E\u72B6\u6001",width:200,options:y}]},{type:"group",children:[{type:"switch",name:"OnlyWhiteListIn",label:"\u53EA\u5141\u8BB8\u767D\u540D\u5355\u5165\u573A",width:200,options:r},{type:"switch",name:"OnlyWhiteListOut",label:"\u53EA\u5141\u8BB8\u767D\u540D\u5355\u51FA\u573A",width:200,options:r},{type:"switch",name:"OnlyOnceIn",label:"\u65E0\u5165\u573A\u8BB0\u5F55\u7684\u4E0D\u80FD\u51FA\u573A",width:200,options:r}]}],modifyTitle:"\u573A\u5730\u4FE1\u606F\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u573A\u5730\u4FE1\u606F",initData:{Id:void 0,SortCode:9999,NightTimes:["00:00:00","00:00:00"]},getPath:"/Parking/Place/getPlaceInfo",setPath:"/Parking/Place/setPlaceInfo",width:888,onCallBack:function(){a.onCallBack&&a.onCallBack()}}),a.Id==null&&(0,t.jsx)(O.ZP,{icon:(0,t.jsx)(b.Z,{}),style:a.style,type:"primary",ghost:!0,onClick:function(){return l.current.get(void 0)},children:"\u6DFB\u52A0"},"place-info-add"),a.Id!=null&&(0,t.jsx)("a",{onClick:function(){l.current.get(a.Id)},title:"\u7F16\u8F91",style:a.style,children:a.Name},"edit")]})}),N=A,S=e(31936),R=e(66309),D=e(85576),H=e(27484),T=e.n(H),F=e(15009),k=e.n(F),E=e(99289),$=e.n(E),U=e(78158),z=i.forwardRef(function(a,M){var m=C.ZP.useMessage(),u=f()(m,2),L=u[0],o=u[1],l=(0,i.useRef)(),y=function(){var r=$()(k()().mark(function c(){var d;return k()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,U.WY)("/Parking/Gate/getSelectListByPlaceId",{method:"POST",data:{Id:a.PlaceId}});case 2:if(d=n.sent,!d.success){n.next=7;break}return n.abrupt("return",d.data?d.data:[]);case 7:return n.abrupt("return",[]);case 8:case"end":return n.stop()}},c)}));return function(){return r.apply(this,arguments)}}();return(0,t.jsxs)(t.Fragment,{children:[o,(0,t.jsx)(g.default,{ref:l,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!0},{name:"PlaceId",type:"hidden",label:"",placeholder:"",required:!0},{name:"Name",type:"input",label:"\u95F8\u673A\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u95F8\u673A\u540D\u79F0",width:380,required:!0},{name:"MainGateId",type:"select",request:y,label:"\u4E3B\u6444\u50CF\u5934",placeholder:"\u8BF7\u9009\u62E9\u4E3B\u6444\u50CF\u5934",required:!1,width:380},{name:"SerialNo",type:"input",label:"\u8BBE\u5907\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u8BBE\u5907\u7F16\u7801",width:380,required:!0},{name:"Direction",type:"select",options:[{label:"\u51FA\u53E3",value:1},{label:"\u5165\u53E3",value:0}],label:"\u8F66\u8F86\u65B9\u5411",placeholder:"\u8BF7\u9009\u62E9\u8F66\u8F86\u65B9\u5411",required:!0,width:380},{name:"ProductModel",type:"input",label:"\u8BBE\u5907\u578B\u53F7",placeholder:"\u8BF7\u8F93\u5165\u8BBE\u5907\u578B\u53F7",width:380,required:!0},{type:"timerange",width:380,name:"OpenTimes",label:"\u5F00\u653E\u65F6\u95F4",placeholder:"\u9009\u62E9\u8BBE\u5907\u5F00\u653E\u65F6\u95F4"}],modifyTitle:"\u95F8\u673A\u4FE1\u606F\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u95F8\u673A\u4FE1\u606F",initData:{Id:void 0,PlaceId:a.PlaceId,SortCode:9999,OpenTimes:["00:00:00","23:59:59"]},getPath:"/Parking/Gate/getGateInfo",setPath:"/Parking/Gate/setGateInfo",width:495,onCallBack:function(){a.onCallBack&&a.onCallBack()}}),a.Id==null&&(0,t.jsx)(O.ZP,{icon:(0,t.jsx)(b.Z,{}),style:a.style,type:"primary",ghost:!0,onClick:function(){return l.current.get(void 0)},children:"\u6DFB\u52A0"},"place-info-add"),a.Id!=null&&(0,t.jsx)("a",{onClick:function(){l.current.get(a.Id)},title:"\u7F16\u8F91",style:a.style,children:a.Name},"edit")]})}),G=z,K=i.forwardRef(function(a,M){var m=C.ZP.useMessage(),u=f()(m,2),L=u[0],o=u[1],l=(0,i.useRef)(),y=(0,i.useState)(!1),r=f()(y,2),c=r[0],d=r[1],s=(0,i.useState)(1),n=f()(s,2),Z=n[0],J=n[1],Q=function(P){return(0,t.jsx)(G,{Id:P.Id,PlaceId:P.PlaceId,Name:P.Name,onCallBack:function(){l.current.reload()}})},X=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"Name",title:"\u540D\u79F0",render:function(P,h){return Q(h)}},{dataIndex:"GateId",title:"\u7F16\u53F7",align:"center"},{dataIndex:"Direction",title:"\u65B9\u5411",render:function(P,h){return[h.Direction==1?"\u51FA\u53E3":"\u5165\u53E3"]},align:"center"},{dataIndex:"ProductModel",title:"\u8BBE\u5907\u578B\u53F7",align:"center"},{dataIndex:"LastHeartBeat",title:"\u5FC3\u8DF3\u65F6\u95F4",align:"center",render:function(P,h){return[T().duration(T()().diff(T()(h.LastHeartBeat))).asMinutes()<5?(0,t.jsx)(R.Z,{color:"green",children:h.LastHeartBeat}):(0,t.jsx)(R.Z,{color:"red",children:h.LastHeartBeat})]}},{dataIndex:"SerialNo",title:"\u8BBE\u5907\u7F16\u7801",align:"center"},{dataIndex:"MainGateName",title:"\u4E3B\u5934",align:"center"},{dataIndex:"Id",title:"\u5220\u9664",align:"center",deleteColumn:!0,deletePath:"/Parking/Gate/deleteGateInfo",deleteMessage:"\u662F\u5426\u786E\u8BA4\u5220\u9664\u8BE5\u95F8\u673A\u4FE1\u606F?"}];return(0,t.jsxs)(x._z,{header:{breadcrumb:{},title:""},children:[o,(0,t.jsx)(D.Z,{destroyOnClose:!0,open:c,onCancel:function(){return d(!1)},afterOpenChange:function(P){},title:a.Name+" \u95F8\u673A\u4FE1\u606F\u5217\u8868",width:1e3,footer:[(0,t.jsx)(G,{Id:void 0,PlaceId:a.PlaceId,onCallBack:function(){l.current.reload()}})],children:(0,t.jsx)(j.Z,{ref:l,formCols:void 0,columns:X,noPage:!0,getPath:"/Parking/Gate/getListByPlaceId?PlaceId="+a.PlaceId,rowKey:"Id"},"jx-area-list-table")},"table-form-modal-"+Z),(0,t.jsx)("a",{onClick:function(){J(Z+1),d(!0)},title:"\u95F8\u673A",children:(0,t.jsx)(S.Z,{})},"gate")]})}),W=K,V=function(){var M=C.ZP.useMessage(),m=f()(M,2),u=m[0],L=m[1],o=(0,i.useRef)(),l=function(s){return(0,t.jsx)(N,{Id:s.Id,Name:s.Name,onCallBack:function(){o.current.reload()}})},y=function(s){return(0,t.jsx)(W,{PlaceId:s.Id,Name:s.Name})},r=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"Name",title:"\u573A\u5730\u540D\u79F0",align:"center",render:function(s,n){return l(n)}},{title:"\u95F8\u673A",align:"center",render:function(s,n){return y(n)}},{dataIndex:"Address",title:"\u5730\u5740",align:"left"},{dataIndex:"IsOpen",title:"\u5F00\u653E",align:"center",render:function(s,n){return[n.IsOpen?"\u662F":"\u5426"]}},{dataIndex:"CreateUserName",title:"\u521B\u5EFA\u4EBA",align:"center"},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",align:"center"},{dataIndex:"Id",title:"\u5220\u9664",align:"center",deleteColumn:!0,deletePath:"/Parking/Place/deletePlaceInfo",deleteMessage:"\u786E\u8BA4\u5220\u9664\u8BE5\u573A\u5730\u4FE1\u606F?"}],c=[(0,t.jsx)(N,{Id:void 0,style:{marginLeft:"10px"},onCallBack:function(){o.current.reload()}})];return(0,t.jsxs)(x._z,{header:{breadcrumb:{},title:""},children:[L,(0,t.jsx)(j.Z,{ref:o,formCols:[],columns:r,tableButtons:c,getPath:"/Parking/Place/getPlaceList",rowKey:"Id"},"jx-area-list-table")]})},Y=V}}]);
