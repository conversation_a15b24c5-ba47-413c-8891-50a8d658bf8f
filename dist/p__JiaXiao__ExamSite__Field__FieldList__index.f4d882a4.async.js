"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1209],{29590:function(G,P){var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M882 272.1V144c0-17.7-14.3-32-32-32H174c-17.7 0-32 14.3-32 32v128.1c-16.7 1-30 14.9-30 31.9v131.7a177 177 0 0014.4 70.4c4.3 10.2 9.6 19.8 15.6 28.9v345c0 17.6 14.3 32 32 32h676c17.7 0 32-14.3 32-32V535a175 175 0 0015.6-28.9c9.5-22.3 14.4-46 14.4-70.4V304c0-17-13.3-30.9-30-31.9zM214 184h596v88H214v-88zm362 656.1H448V736h128v104.1zm234 0H640V704c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v136.1H214V597.9c2.9 1.4 5.9 2.8 9 4 22.3 9.4 46 14.1 70.4 14.1s48-4.7 70.4-14.1c13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 13.8-5.8 26.8-13.2 38.7-22.1.2-.1.4-.1.6 0a180.4 180.4 0 0038.7 22.1c22.3 9.4 46 14.1 70.4 14.1 24.4 0 48-4.7 70.4-14.1 3-1.3 6-2.6 9-4v242.2zm30-404.4c0 59.8-49 108.3-109.3 108.3-40.8 0-76.4-22.1-95.2-54.9-2.9-5-8.1-8.1-13.9-8.1h-.6c-5.7 0-11 3.1-13.9 8.1A109.24 109.24 0 01512 544c-40.7 0-76.2-22-95-54.7-3-5.1-8.4-8.3-14.3-8.3s-11.4 3.2-14.3 8.3a109.63 109.63 0 01-95.1 54.7C233 544 184 495.5 184 435.7v-91.2c0-.3.2-.5.5-.5h655c.3 0 .5.2.5.5v91.2z"}}]},name:"shop",theme:"outlined"};P.Z=a},91410:function(G,P,a){a.r(P),a.d(P,{default:function(){return me}});var K=a(15009),c=a.n(K),U=a(99289),w=a.n(U),Y=a(5574),g=a.n(Y),X=a(51477),k=a(78158),q=a(11774),L=a(45360),A=a(83622),Q=a(85576),z=a(56595),d=a(67294),_=a(29590),ee=a(96750);function B(){return B=Object.assign?Object.assign.bind():function(i){for(var C=1;C<arguments.length;C++){var v=arguments[C];for(var f in v)Object.prototype.hasOwnProperty.call(v,f)&&(i[f]=v[f])}return i},B.apply(this,arguments)}const ae=(i,C)=>d.createElement(ee.Z,B({},i,{ref:C,icon:_.Z}));var te=d.forwardRef(ae),ne=a(97857),re=a.n(ne),le=a(89545),D=a(58930),H=a(23750),se=a(88634),ie=a(42509),J=a(18922),t=a(85893),ue=d.forwardRef(function(i,C){var v=L.ZP.useMessage(),f=g()(v,2),E=f[0],V=f[1],h=(0,d.useRef)(),S=(0,d.useRef)(),M=function(){var r=w()(c()().mark(function e(){return c()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.abrupt("return",(0,k.WY)("/Jx/Pay/CostType/getCostTypeOutTableSelectList",re()({method:"POST",data:{}},{})));case 1:case"end":return l.stop()}},e)}));return function(){return r.apply(this,arguments)}}(),b=(0,d.useState)([]),O=g()(b,2),j=O[0],x=O[1],I=function(){var r=w()(c()().mark(function e(s){var l,u,T,m;return c()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(l=[],s!=null){n.next=5;break}return n.abrupt("return",[]);case 5:if(s!="1"){n.next=12;break}return n.next=8,(0,H._1)();case 8:u=n.sent,u&&u.success?l=u.data:l=[],n.next=24;break;case 12:if(s!="2"){n.next=19;break}return n.next=15,(0,H.kA)();case 15:T=n.sent,T.success?l=T.data:l=[],n.next=24;break;case 19:if(s!="3"){n.next=24;break}return n.next=22,(0,H.cL)();case 22:m=n.sent,m&&m.success?l=m.data:l=[];case 24:S.current.updateOptions(l),h.current.setValue("OutValue",void 0);case 26:case"end":return n.stop()}},e)}));return function(s){return r.apply(this,arguments)}}(),Z=(0,t.jsx)(J.Z.Item,{name:"OutTable",label:"\u89E6\u53D1\u7C7B\u578B",style:{marginBottom:0},children:(0,t.jsx)(D.Z,{request:M,title:"",placeholder:"\u8BF7\u9009\u62E9\u89E6\u53D1\u7C7B\u578B",listHeight:400,multiple:!1,allowClear:!0,onChange:function(){var r=w()(c()().mark(function e(s){return c()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:h.current.setValue("OutTable",s),s==null||s.length==0?I(void 0):I(s[0]);case 2:case"end":return u.stop()}},e)}));return function(e){return r.apply(this,arguments)}}()})}),N=(0,t.jsx)(J.Z.Item,{name:"OutValue",label:"\u89E6\u53D1\u7684\u503C",style:{marginBottom:0},rules:[{validator:function(){var r=w()(c()().mark(function s(l,u){return c()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(!(h.current.getValue("OutTable")!=null&&u==null)){m.next=3;break}throw E.error("\u8BF7\u9009\u62E9\u89E6\u53D1\u7684\u503C"),new Error;case 3:case"end":return m.stop()}},s)}));function e(s,l){return r.apply(this,arguments)}return e}()}],children:(0,t.jsx)(D.Z,{ref:S,options:j,title:"",placeholder:"\u8BF7\u9009\u62E9\u89E6\u53D1\u7684\u503C",listHeight:600,multiple:!1,allowClear:!0,onChange:function(e){h.current.setValue("OutValue",e)}})});return(0,t.jsxs)(t.Fragment,{children:[V,(0,t.jsx)(le.default,{ref:h,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1},{name:"NickName",type:"input",label:"\u8003\u573A\u6635\u79F0",placeholder:"\u8BF7\u8F93\u5165\u8003\u573A\u6635\u79F0",width:"100%",required:!0},{name:"KeMuId",type:"select",options:[{label:"\u79D1\u76EE\u4E00",value:1},{label:"\u79D1\u76EE\u4E8C",value:2},{label:"\u79D1\u76EE\u4E09",value:3},{label:"\u79D1\u76EE\u4E8C\u4E09",value:9}],label:"\u8003\u8BD5\u79D1\u76EE",placeholder:"\u9009\u62E9\u8003\u8BD5\u79D1\u76EE",width:"100%",required:!0},{name:"TenantId",type:"input",label:"\u516C\u53F8\u7F16\u53F7",disabled:!0,width:"100%"},{name:"Id",type:"input",label:"\u7CFB\u7EDF\u7F16\u53F7",disabled:!0,width:"100%"},{name:"kc",type:"textarea",label:"\u8003\u573A\u540D\u79F0",width:"100%",required:!0,placeholder:"\u8BF7\u8F93\u5165\u8003\u573A\u540D\u79F0\uFF0C\u591A\u4E2A\u540D\u5B57\u7528;\u9694\u5F00",height:80},{name:"CityIds",type:"cascader",label:"\u6240\u5728\u533A\u57DF",request:se.NM,width:"100%",height:"600px",required:!0},{name:"Address",type:"textarea",label:"\u8003\u573A\u5730\u5740",width:"100%",required:!0,placeholder:"\u8BF7\u8F93\u5165\u8003\u573A\u5730\u5740",height:80},{name:"Location",type:"input",label:"\u8003\u573A\u5B9A\u4F4D",width:"100%",required:!0,placeholder:"\u8BF7\u8F93\u5165\u8003\u573A\u5B9A\u4F4D"},{name:"fzjg",type:"input",label:"\u53D1\u8BC1\u673A\u5173",width:"100%",required:!0,placeholder:"\u8BF7\u8F93\u5165\u53D1\u8BC1\u673A\u5173\uFF0C\u4F8B\u5982\uFF1A\u6E58A"},{type:"divider"},{type:"group",children:[{name:"ChoosePlanQueenSelf",type:"switch",label:"\u53EF\u4EE5\u81EA\u9009\u573A\u6B21",width:"100%"},{name:"NotMustOrderExam",type:"switch",label:"\u4E0D\u7EA6\u8003\u53EF\u8D2D\u4E70",width:"100%"}]},{type:"group",children:[{name:"CouponMustAudit",type:"switch",label:"\u8FD4\u73B0\u9700\u8981\u5BA1\u6838",width:"100%"},{name:"CouponMustComplete",type:"switch",label:"\u8FD4\u73B0\u9700\u8981\u8BAD\u7EC3",width:"100%"}]},{name:"AfterNoonStartTime",type:"timepicker",label:"\u4E0B\u5348\u5F00\u573A",placeholder:"\u8BF7\u9009\u62E9\u4E0B\u5348\u5F00\u573A\u65F6\u95F4",width:"100%"},{name:"AfterNoonKeyWords",type:"input",label:"\u5173\u952E\u5B57\u8BCD",width:"100%",placeholder:"\u8BF7\u8F93\u5165\u4E0B\u5348\u573A\u5173\u952E\u5B57,\u7528;\u5206\u5F00"}],modifyTitle:"\u8003\u8BD5\u573A\u5730\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u8003\u8BD5\u573A\u5730",initData:{Id:void 0,SortCode:9999},getPath:"/Jx/ExamSite/Field/getFieldInfo",onGetCallBack:function(e){return e.success&&(e.data.OutTable==""&&(e.data.OutTable=void 0),e.data.OutValue==""&&(e.data.OutValue=void 0)),e},onLoad:function(e){e!=null&&e.OutTable&&I(e.OutTable)},setPath:"/Jx/ExamSite/Field/setFieldInfo",width:500,onCallBack:function(){i.onCallBack&&i.onCallBack()}}),i.Id==null&&(0,t.jsx)(A.ZP,{icon:(0,t.jsx)(ie.Z,{}),ghost:!0,style:i.style,type:"primary",onClick:function(){return h.current.get(void 0)},children:"\u6DFB\u52A0"},"jx-area-add"),i.Id!=null&&(0,t.jsx)("a",{onClick:function(){h.current.get(i.Id)},title:"\u7F16\u8F91",style:i.style,children:i.NickName},"edit")]})}),W=ue,de=a(1291),oe=a(48054),ce=d.forwardRef(function(i,C){var v=L.ZP.useMessage(),f=g()(v,2),E=f[0],V=f[1],h=(0,d.useState)(!1),S=g()(h,2),M=S[0],b=S[1],O=(0,d.useState)(!1),j=g()(O,2),x=j[0],I=j[1],Z=(0,d.useState)(""),N=g()(Z,2),r=N[0],e=N[1],s=function(){e(""),I(!0),b(!0),(0,k.WY)("/Jx/ExamSite/Cashier/Field/getFieldMiniProgramQrCode",{method:"POST",data:{Id:i.Id}}).then(function(u){u.success?(e(u.data),I(!1)):b(!1)})};return(0,t.jsxs)(t.Fragment,{children:[V,(0,t.jsx)(Q.Z,{open:M,onCancel:function(){b(!1)},width:300,title:"\u7EBF\u4E0A\u4ED8\u6B3E",footer:!1,loading:x,className:"no-padding",children:(0,t.jsxs)("div",{style:{paddingTop:"20px",textAlign:"center"},children:[x&&(0,t.jsx)(oe.Z.Image,{active:x,style:{width:250,height:250}}),!x&&(0,t.jsx)(z.Z,{width:250,src:r.startsWith("data:image")?r:"data:image/png;base64,"+r,preview:!1},"barCodeImage")]})}),(0,t.jsx)("a",{onClick:s,children:(0,t.jsx)(de.Z,{})})]})}),fe=ce,he=function(){var C=L.ZP.useMessage(),v=g()(C,2),f=v[0],E=v[1],V=(0,d.useState)(!1),h=g()(V,2),S=h[0],M=h[1],b=(0,d.useState)(""),O=g()(b,2),j=O[0],x=O[1],I=(0,d.useState)(!1),Z=g()(I,2),N=Z[0],r=Z[1],e=(0,d.useRef)(),s=function(){var p=w()(c()().mark(function n(){var y,R;return c()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return r(!0),o.prev=1,o.next=4,(0,k.ZP)("/Jx/ExamSite/Cashier/Field/getFieldSelfLoginByTenantId",{method:"POST",data:{}});case 4:y=o.sent,y.success?(R="data:image/png;base64,".concat(y.data),x(R),M(!0)):f.error("\u83B7\u53D6\u4E8C\u7EF4\u7801\u5931\u8D25"),o.next=12;break;case 8:o.prev=8,o.t0=o.catch(1),console.error("Error fetching QR code:",o.t0),f.error("\u83B7\u53D6\u4E8C\u7EF4\u7801\u5931\u8D25");case 12:return o.prev=12,r(!1),o.finish(12);case 15:case"end":return o.stop()}},n,null,[[1,8,12,15]])}));return function(){return p.apply(this,arguments)}}(),l=function(){M(!1),x("")},u=function(n){return(0,t.jsx)(W,{Id:n.Id,NickName:n.NickName,onCallBack:function(){e.current.reload()}})},T=function(n){return(0,t.jsx)(fe,{Id:n.Id,NickName:n.NickName})},m=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"NickName",title:"\u663E\u793A\u6635\u79F0",align:"center",render:function(n,y){return u(y)}},{dataIndex:"Id",title:"\u5546\u57CE",align:"center",render:function(n,y){return T(y)}},{dataIndex:"KeMuIdValue",title:"\u79D1\u76EE",align:"center"},{dataIndex:"fzjg",title:"\u53D1\u8BC1\u673A\u5173",align:"center"},{dataIndex:"kc",title:"\u8003\u573A\u540D\u79F0",align:"left"},{dataIndex:"Address",title:"\u5730\u5740",align:"center"}];return(0,t.jsxs)(q._z,{header:{breadcrumb:{},title:""},children:[E,(0,t.jsx)(X.Z,{ref:e,formCols:[],columns:m,tableButtons:[(0,t.jsxs)(A.ZP.Group,{children:[(0,t.jsx)(W,{Id:void 0,NickName:void 0,onCallBack:function(){e.current.reload()}}),(0,t.jsx)(A.ZP,{ghost:!0,type:"primary",icon:(0,t.jsx)(te,{}),onClick:s,loading:N,children:"\u5546\u57CE"},"mall-qr")]},"button-group")],search:function(){var p=w()(c()().mark(function n(y){var R,$;return c()().wrap(function(F){for(;;)switch(F.prev=F.next){case 0:return F.next=2,(0,k.ZP)("/Jx/ExamSite/Field/getFieldList",{method:"POST",data:y});case 2:return R=F.sent,$=R.data,F.abrupt("return",$);case 5:case"end":return F.stop()}},n)}));return function(n){return p.apply(this,arguments)}}(),rowKey:"Id"},"field-list-table"),(0,t.jsx)(Q.Z,{title:"\u5546\u57CE\u4E8C\u7EF4\u7801",open:S,onCancel:l,footer:null,centered:!0,children:(0,t.jsx)("div",{style:{textAlign:"center",padding:"20px"},children:j?(0,t.jsx)(z.Z,{src:j,alt:"\u5546\u57CE\u4E8C\u7EF4\u7801",style:{maxWidth:"300px"},preview:!1}):(0,t.jsx)("div",{style:{width:"200px",height:"200px",background:"#f0f0f0",margin:"0 auto",display:"flex",alignItems:"center",justifyContent:"center"},children:"\u52A0\u8F7D\u4E2D..."})})})]})},me=he}}]);
