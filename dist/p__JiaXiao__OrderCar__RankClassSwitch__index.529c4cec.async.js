"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7367],{5148:function(b,l,e){e.r(l);var C=e(15009),o=e.n(C),D=e(99289),P=e.n(D),c=e(5574),u=e.n(c),i=e(67294),h=e(45360),v=e(26410),O=e(36447),g=e(11774),I=e(94815),R=e(98302),f=e(52151),j=e(78158),x=e(27484),m=e.n(x),T=e(28508),p=e(60433),U=e(43425),a=e(85893),A=function(){var y=h.ZP.useMessage(),E=u()(y,2),S=E[0],B=E[1],F=i.useRef(),L=i.useState(),M=u()(L,2),Z=M[0],Y=M[1];return(0,a.jsxs)(a.Frag<PERSON>,{children:[B,(0,a.jsx)(g._z,{header:{breadcrumb:{},title:""},children:(0,a.jsx)(I.Rs,{search:{collapseRender:!1,collapsed:!1},ghost:!1,rowKey:"name",headerTitle:"\u6392\u73ED\u5F00\u5173",request:P()(o()().mark(function _(){var d,s,t=arguments;return o()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return d=t.length>0&&t[0]!==void 0?t[0]:{},n.next=3,(0,j.ZP)("/JiaXiao/OrderCar/RankClass/getRankClassDetailList",{method:"POST",data:d});case 3:return s=n.sent,n.abrupt("return",{success:s.success,data:s.data.data,total:s.data.total});case 5:case"end":return n.stop()}},_)})),pagination:{showSizeChanger:!0,showQuickJumper:!0,defaultPageSize:10},grid:{gutter:16,column:5},metas:{OrderDate:{title:"\u9884\u7EA6\u65E5\u671F",valueType:"dateRange",dataIndex:"OrderDates",initialValue:[m()().format("YYYY-MM-DD"),m()().add(6,"days").format("YYYY-MM-DD")]},KeMuId:{dataIndex:"KeMuId",title:"\u57F9\u8BAD\u79D1\u76EE",valueType:"select",valueEnum:{2:{text:"\u79D1\u76EE\u4E8C",value:"2"},3:{text:"\u79D1\u76EE\u4E09",value:"3"}}},StaffId:{dataIndex:"StaffId",title:"\u5458\u5DE5\u9009\u62E9",valueType:"select",renderFormItem:function(d,s){var t=s.onChange,r=s.value;return(0,a.jsx)(R.Z,{autoUpdateText:!1,value:r,onChange:t,width:"100%"})}},CarId:{dataIndex:"CarId",title:"\u8F66\u8F86\u9009\u62E9",valueType:"select",renderFormItem:function(d,s){var t=s.onChange,r=s.value;return(0,a.jsx)(f.Z,{autoUpdateText:!1,value:r,onChange:t,width:"100%"})}},title:{dataIndex:"OrderTime",search:!1},actions:{cardActionProps:"actions",search:!1,render:function(){return[(0,a.jsxs)("a",{children:[(0,a.jsx)(T.Z,{})," \u5173\u95ED"]},"close"),(0,a.jsxs)("a",{children:[(0,a.jsx)(p.Z,{})," \u540D\u5355"]},"detail"),(0,a.jsxs)("a",{children:[(0,a.jsx)(U.Z,{})," \u8BBE\u7F6E"]},"setting")]}},content:{search:!1,render:function(d,s,t,r){return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{style:{width:"100%"},children:[(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)("div",{style:{textAlign:"left"},children:s.xm}),(0,a.jsx)("div",{style:{textAlign:"right"},children:s.CarNumber})]}),(0,a.jsx)("div",{style:{marginTop:"20px"},children:(0,a.jsx)(v.Z,{percent:s.Count/s.MaxPeopleCount})})]})})}}}})})]})},W=function(){return(0,a.jsx)(O.Z,{children:(0,a.jsx)(A,{})})};l.default=W}}]);
